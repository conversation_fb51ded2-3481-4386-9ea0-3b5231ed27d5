export const isProduction = import.meta.env.MODE === 'production';

export const keyMapMetadata: Record<string, string> = {
  category: 'Thư mục gốc',
  fileName: 'Tên tệp',
  fullPath: 'Đ<PERSON>ờng dẫn đầy đủ',
  createAt: '<PERSON><PERSON><PERSON> tạo',
  creator: 'Ngư<PERSON><PERSON> tạo',
  creatorId: 'ID người tạo',
  DataSource: 'Nguồn dữ liệu',
  SuggestedAICategories: 'Danh mục AI gợi ý',
  KeywordAISuggestion: 'Từ khóa AI gợi ý',
  SummaryOfSuggestedAI: 'Tóm tắt AI gợi ý',
  AccessLevel: 'Cấp độ truy cập',
};

export const isTextarea = (key: string) => {
  if (
    key === 'SummaryOfSuggestedAI' ||
    key === 'KeywordAISuggestion' ||
    key === 'SuggestedAICategories'
  ) {
    return true;
  }

  return false;
};

export const isDisableField = (key: string) => {
  if (
    key === 'fullPath' ||
    key === 'createAt' ||
    key === 'creator' ||
    key === 'creatorId' ||
    key === 'DataSource' ||
    key === 'AccessLevel' ||
    key === 'category' ||
    key === 'fileName'
  ) {
    return true;
  }

  return false;
};

export const checkConditionUploadFiles = (files: FileList, toast: any) => {
  const maxFiles = 30;
  const maxSize = 2 * 1024 * 1024 * 10;

  if (files.length > maxFiles) {
    toast({
      title: 'Lỗi tải lên',
      description: `Bạn chỉ có thể tải lên tối đa ${maxFiles} tệp.`,
      variant: 'destructive',
    });
    return true;
  }

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    if (file && file.size > maxSize) {
      toast({
        title: 'Lỗi tải lên',
        description: `Tệp ${file.name} vượt quá giới hạn kích thước 20MB.`,
        variant: 'destructive',
      });
      return true;
    }
  }
  return false;
};

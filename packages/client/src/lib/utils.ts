import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

export function getUserInfoFromLocal(): any {
  let dataLogin: any = localStorage.getItem('dataLogin');
  let user: any;
  try {
    if (dataLogin) {
      dataLogin = JSON.parse(dataLogin);
      user = dataLogin.user;
    }
  } catch (error) {
    console.error('Error parsing dataLogin from localStorage:', error);
    dataLogin = null;
  }
  return user;
}

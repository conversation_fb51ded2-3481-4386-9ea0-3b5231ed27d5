import axios from 'axios';
import { Briefcase, Loader2, RotateCw } from 'lucide-react';
import { memo, useEffect, useState } from 'react';

import TooltipS from '@/components/ui/ToolTipS';
import { Button } from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Toaster } from '@/components/ui/toaster.tsx';
import { useToast } from '@/hooks/use-toast.ts';

interface JobData {
  id: number;
  job_name: string;
  status: string;
  created_at: string;
  updated_at: string;
  execution_time_seconds: number;
  error: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface ApiResponse {
  data: JobData[];
  pagination: PaginationData;
}

const ITEMS_PER_PAGE = 10;

export const Job = memo<{
  apiBaseUrl: string;
}>(({ apiBaseUrl = import.meta.env['VITE_APP_API_URL'] }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [jobs, setJobs] = useState<JobData[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: ITEMS_PER_PAGE,
    totalPages: 1,
  });
  const [loading, setLoading] = useState(true);
  const [loadingPage, setLoadingPage] = useState<number | null>(null);
  const [retryingJobs, setRetryingJobs] = useState<Set<string>>(new Set());
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchJobs = async (page: number) => {
    try {
      setLoadingPage(page);
      const response = await axios.get<ApiResponse>(`${apiBaseUrl}/api/jobs`, {
        params: {
          page: page.toString(),
          limit: ITEMS_PER_PAGE.toString(),
        },
      });
      setJobs(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch jobs. Please try again.',
      });
    } finally {
      setLoadingPage(null);
      setLoading(false);
    }
  };

  useEffect(() => {
    void fetchJobs(currentPage);
  }, [currentPage, apiBaseUrl]);

  const handleRetry = async (jobId: number) => {
    setRetryingJobs((prev) => new Set(prev).add(jobId.toString()));

    try {
      await axios.post(`${apiBaseUrl}/api/retry-job`, { jobId });
      toast({
        title: 'Job Restarted',
        description: `Job ${jobId} has been successfully restarted.`,
      });
      void fetchJobs(currentPage); // Refresh the list after retry
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Retry Failed',
        description: `Failed to restart job ${jobId}. Please try again.`,
      });
    } finally {
      setRetryingJobs((prev) => {
        const newSet = new Set(prev);
        newSet.delete(jobId.toString());
        return newSet;
      });
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    setCurrentPage(1);
    await fetchJobs(1);
    setIsRefreshing(false);
  };

  const formatStartTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const formatExecutionTime = (seconds: number | string): string => {
    seconds = parseFloat(seconds as string);

    if (seconds < 60) {
      return `${(seconds as number).toFixed(2)}s`;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = (seconds % 60).toFixed(0);
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div>
      <div className="flex items-center gap-2 mb-6">
        <Briefcase className="w-8 h-8 text-primary" />
        <h2 className="text-3xl font-bold tracking-tight">Job Management</h2>
        <Button
          variant="outline"
          size="icon"
          onClick={handleRefresh}
          disabled={isRefreshing || loading}
          className="ml-2"
        >
          <RotateCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Tên Công Việc</TableHead>
              <TableHead>Thời Gian Bắt Đầu</TableHead>
              <TableHead>Thời Gian Cập Nhật</TableHead>
              <TableHead>Thời Gian Thực Thi</TableHead>
              <TableHead>Trạng Thái</TableHead>
              <TableHead>Thao Tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading && jobs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  <Loader2 className="w-6 h-6 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : (
              jobs.map((job) => (
                <TableRow key={job.id}>
                  <TableCell className="!w-[100px]">{job.id}</TableCell>
                  <TableCell className="font-medium">
                    {job.job_name?.replace('Upload file', 'Summary')}
                  </TableCell>
                  <TableCell className="!w-[200px]">{formatStartTime(job.created_at)}</TableCell>
                  <TableCell className="!w-[200px]">{formatStartTime(job.updated_at)}</TableCell>
                  <TableCell className="!w-[150px]">
                    {formatExecutionTime(job.execution_time_seconds)}
                  </TableCell>

                  <TableCell className="!w-[200px]">
                    <TooltipS
                      content={
                        job.status === 'fail' && job.error ? (
                          <div className="font-medium text-[16px] max-w-[90dvw]">{job.error}</div>
                        ) : null
                      }
                    >
                      <div
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium truncate text-ellipsis overflow-hidden whitespace-nowrap max-w-[30dvw]
                        ${
                          job.status === 'done'
                            ? 'bg-green-100 text-green-800'
                            : job.status === 'fail'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {job.status === 'fail' ? 'FAILED' : job.status?.toUpperCase()}
                      </div>
                    </TooltipS>
                  </TableCell>

                  <TableCell className="!w-[200px]">
                    {job.status === 'fail' ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRetry(job.id)}
                        disabled={retryingJobs.has(job.id.toString())}
                        className="h-[32px]"
                      >
                        {retryingJobs.has(job.id.toString()) ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Đang thử lại...
                          </>
                        ) : (
                          'Thử Lại'
                        )}
                      </Button>
                    ) : (
                      <div className="h-[32px]" />
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="mt-4">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
              .filter(
                (page) =>
                  page === 1 || page === pagination.totalPages || Math.abs(page - currentPage) <= 1,
              )
              .map((page, index, array) => (
                <>
                  {index > 0 && array[index - 1] !== page - 1 && (
                    <PaginationItem key={page + '...'}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                      className={`cursor-pointer ${loadingPage !== null ? 'pointer-events-none' : ''}`}
                    >
                      {loadingPage === page ? <Loader2 className="h-4 w-4 animate-spin" /> : page}
                    </PaginationLink>
                  </PaginationItem>
                </>
              ))}

            <PaginationItem>
              <PaginationNext
                onClick={() => setCurrentPage((p) => Math.min(pagination.totalPages, p + 1))}
                className={
                  currentPage === pagination.totalPages || loadingPage !== null
                    ? 'pointer-events-none opacity-50'
                    : 'cursor-pointer'
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
      <Toaster />
    </div>
  );
});

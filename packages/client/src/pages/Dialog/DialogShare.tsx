import { ShareIcon } from 'lucide-react';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DialogShareProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  fileName: string;
}

const DialogShare: React.FC<DialogShareProps> = ({ open, onOpenChange, fileName }) => {
  const [email, setEmail] = useState('');
  const [permission, setPermission] = useState('viewer');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShareIcon className="h-5 w-5" />
            Chia sẻ "{fileName}"
          </DialogTitle>
          <DialogDescription>
            Chia sẻ file này với người khác để họ có thể xem, chỉnh sửa hoặc tải về.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="email">Email hoặc tên</Label>
            <div className="flex gap-2">
              <Input
                id="email"
                placeholder="Nhập email hoặc tên"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <Select value={permission} onValueChange={setPermission}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Chọn quyền" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">Chỉ xem</SelectItem>
                  <SelectItem value="editor">Chỉnh sửa</SelectItem>
                  <SelectItem value="downloader">Tải về</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="rounded-md border p-4">
            <h4 className="mb-2 font-medium">Những người có quyền truy cập</h4>
            <div className="text-sm text-muted-foreground">Chưa có ai được chia sẻ</div>
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={() => onOpenChange(false)}>Chia sẻ</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DialogShare;

import axios from 'axios';
import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast.ts';

interface DialogRemoveFilesProps {
  apiBaseUrl?: string;
  openDialogRename: boolean;
  setOpenDialogRename: Dispatch<SetStateAction<boolean>>;
  setFileToMove: Dispatch<SetStateAction<any>>;
  fileToMove: any;
  refreshFiles: () => Promise<void>;
  ignoreCache: {
    current: boolean;
  };
}

const DialogRemoveFiles: React.FC<DialogRemoveFilesProps> = ({
  openDialogRename,
  setOpenDialogRename,
  apiBaseUrl,
  setFileToMove,
  fileToMove,
  refreshFiles,
  ignoreCache,
}) => {
  const [structureMap, setStructureMap] = useState<Record<string, any>>({});
  const [isRemoving, setIsRemoving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectPath, setSelectPath] = useState('');

  const { toast } = useToast();

  useEffect(() => {
    if (openDialogRename) {
      fetchS3Folders(selectPath);
    }
  }, [selectPath, openDialogRename]);

  const fetchS3Folders = async (path: string) => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${apiBaseUrl}/api/list-s3-folders?prefix=${path}`);
      setStructureMap(response?.data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Unable to load S3 folder list',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleMoveFile = async () => {
    setIsRemoving(true);
    try {
      const oldPath = fileToMove.key;
      // Check if the oldPath is the same as the new path (considering trailing slash)
      const normalizedOldPath = oldPath.endsWith('/') ? oldPath : `${oldPath}/`;
      const normalizedNewPath = selectPath.endsWith('/') ? selectPath : `${selectPath}/`;

      if (normalizedOldPath === normalizedNewPath || oldPath === selectPath) {
        toast({
          title: 'Lỗi',
          description: 'Không thể di chuyển file đến cùng vị trí',
          variant: 'destructive',
        });
        setIsRemoving(false);
        return;
      }
      await axios.post(`${apiBaseUrl}/api/move`, {
        oldPath,
        newPath: selectPath,
      });

      // Refresh the file list
      ignoreCache.current = true;
      await refreshFiles();

      // Show success toast
      toast({
        title: 'Đã di chuyển',
        description: `Đã di chuyển thành công "${fileToMove.name}" đến "${selectPath}"`,
      });
      setOpenDialogRename(false);
      setFileToMove(null);
      setSelectPath('');
    } catch (error) {
      console.error('Error renaming item:', error);
      toast({
        title: 'Lỗi',
        description: 'Không thể đổi tên',
        variant: 'destructive',
      });
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <Dialog
      open={openDialogRename}
      onOpenChange={(e) => {
        if (!e) {
          setOpenDialogRename(false);
          setFileToMove(null);
          setSelectPath('');
        }
      }}
    >
      <DialogContent className="max-h-[700px] pb-3 px-1 s3-explr-input-group max-w-5xl w-[90%] p-6 bg-white rounded-lg shadow-lg">
        <DialogHeader className="mb-4 w-full h-full">
          <nav className="mb-4">
            <ul className="flex items-center space-x-2 text-sm text-gray-600">
              <li
                className="cursor-pointer hover:text-blue-600 transition-colors"
                onClick={() => setSelectPath('')}
              >
                Root
              </li>
              {selectPath.split('/').map((path, index, array) => (
                <li
                  key={index}
                  className="cursor-pointer hover:text-blue-600 transition-colors"
                  onClick={() =>
                    setSelectPath(path === 'Root' ? '' : array.slice(0, index + 1).join('/') + '/')
                  }
                >
                  {path}
                </li>
              ))}
            </ul>
          </nav>
          <DialogTitle className="text-xl font-semibold text-gray-800">
            Di chuyển{' '}
            <span className="text-sm text-gray-500">
              ( file Hoặc folder sẽ được di chuyển đến thư mục{' '}
              {selectPath === '' ? 'Root' : selectPath} )
            </span>
          </DialogTitle>
          <DialogDescription className="text-gray-600 w-full h-[50dvh] max-h-[500px] overflow-hidden overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center py-4">
                <div className="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12"></div>
              </div>
            ) : (
              <ul className="space-y-2">
                {Object.keys(structureMap)?.map((folder) => (
                  <li
                    className="cursor-pointer hover:bg-gray-100 p-2 rounded transition-colors"
                    onClick={() => setSelectPath(folder)}
                    key={folder}
                  >
                    {folder}
                  </li>
                ))}
              </ul>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => setOpenDialogRename(false)}
            className="px-4 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-100 transition-colors"
          >
            Cancel
          </Button>
          <Button onClick={handleMoveFile} disabled={isRemoving} className="s3-explr-crawl-btn">
            Move
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DialogRemoveFiles;

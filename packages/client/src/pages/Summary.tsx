import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
} from '@tanstack/react-table';
import axios from 'axios';
import { Eye, Loader2, X } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import TooltipS from '@/components/ui/ToolTipS';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';

interface SummaryProps {
  apiBaseUrl?: string;
}

interface SummaryData {
  id: string;
  prefix: string;
  model: string;
  fileName: string;
  code: string;
  month: string;
  year: string;
  content: string;
  status: string;
  note: string;
  lastUpdate: string;
  stock_id: string;
}

const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const Summary: React.FC<SummaryProps> = ({
  apiBaseUrl = import.meta.env['VITE_APP_API_URL'],
}) => {
  const [data, setData] = useState<SummaryData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loadingPage, setLoadingPage] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const extractMonthYearFromFilename = (filename: string) => {
    const match = filename.match(/\d{6}/);
    if (!match) return { month: '', year: '' };

    const dateStr = match[0];
    const month = dateStr.substring(0, 2);
    const year = dateStr.substring(2);

    return { month, year };
  };

  const mapConversation = (items: any) => {
    const summaryTransfer = items.map((item: any) => {
      let month = item?.month || '';
      let year = item?.year || '';

      if ((!month || !year) && item?.file_name) {
        const extracted = extractMonthYearFromFilename(item.file_name);
        month = month || extracted.month;
        year = year || extracted.year;
      }

      return {
        id: item?.id,
        prefix: item?.prefix || '',
        model: item?.model || '',
        fileName: item?.file_name || '',
        code: item?.stock_id || '',
        month,
        year,
        content: item?.text_summary || '',
        status: item?.status || '',
        note: item?.note || '',
        lastUpdate: item?.updated_at,
        stock_id: item?.stock_id,
      };
    });
    return summaryTransfer;
  };

  const handleConversation = (summaryRes: any) => {
    if (!summaryRes?.data) return [];
    const data = mapConversation(summaryRes?.data);
    setData(data);
    setTotalPages(summaryRes.pagination.totalPages);
    return;
  };

  const fetchData = async (page = 1, limit = 15) => {
    setLoadingPage(page);
    setIsLoading(true);
    try {
      const response = await axios.get(`${apiBaseUrl}/api/latest-summaries`, {
        params: { page, limit, search: searchQuery },
      });

      if (response?.status === 200) {
        handleConversation(response.data);
      } else {
        console.error(response);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoadingPage(null);
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (debouncedSearchQuery) {
      setCurrentPage(1);
    }
    void fetchData(currentPage);
  }, [currentPage, debouncedSearchQuery]);

  const columns = useMemo<ColumnDef<any>[]>(() => {
    const baseColumns = [
      {
        id: 'code',
        header: 'Mã',
        accessorKey: 'code',
        cell: ({ row }: any) => {
          return <span className="flex flex-col">{row.original.code}</span>;
        },
      },
      {
        id: 'month',
        header: 'Tháng',
        accessorKey: 'month',
        cell: ({ row }: any) => {
          const month = row.original.month;
          const year = row.original.year;
          return (
            <span className="flex flex-col">{`${month.toString().padStart(2, '0')}/${year}`}</span>
          );
        },
      },
      {
        id: 'content',
        header: 'Nội dung',
        accessorKey: 'content',
        cell: ({ row }: any) => {
          const content: string = row.getValue('content');
          return (
            <div className="text-start truncate text-ellipsis overflow-hidden whitespace-nowrap max-w-[30dvw]">
              {content}
            </div>
          );
        },
      },
      {
        id: 'status',
        header: 'Trạng thái',
        accessorKey: 'status',
        cell: ({ row }: any) => {
          const status = row.original.status;
          const isPublished = status !== 'unpublish';
          return (
            <span
              className={`inline-flex items-center rounded-full px-2 py-0 text-[10px] font-bold ${
                isPublished
                  ? 'bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20'
                  : 'bg-gray-50 text-gray-600 ring-1 ring-inset ring-gray-500/20'
              }`}
            >
              {isPublished ? 'PUBLISHED' : 'UNPUBLISHED'}
            </span>
          );
        },
      },
      {
        id: 'note',
        header: 'Ghi chú',
        accessorKey: 'note',
        cell: ({ row }: any) => {
          return (
            <TooltipS
              content={
                <div className="font-medium text-[16px] max-w-[90dvw]">{row.original.note}</div>
              }
            >
              <div className="text-start truncate text-ellipsis overflow-hidden whitespace-nowrap max-w-[20dvw]">
                {row.original.note}
              </div>
            </TooltipS>
          );
        },
      },
      {
        id: 'last-update',
        header: 'Cập nhật',
        accessorKey: 'last-update',
        cell: ({ row }: any) => {
          const date = new Date(row.original.lastUpdate);
          const day = date.getDate();
          const month = date.getMonth() + 1;
          const formattedDate = `ngày ${day} tháng ${month}`;

          return <span className="flex flex-col">{formattedDate}</span>;
        },
      },
      {
        accessorKey: 'actions',
        header: () => {
          return <div className="font-medium text-right">Hành động</div>;
        },
        cell: ({ row }: any) => {
          const [loading, setLoading] = useState(false);
          const [openPopup, setOpenPopup] = useState(false);
          const [showFullContent, setShowFullContent] = useState(false);
          const { handleSubmit, register, reset } = useForm();
          const id = (row.original as any).id;
          const status: string = row.getValue('status');
          const content: string = row.getValue('content');
          const [publishLoading, setPublishLoading] = useState(false);

          const handleTogglePublish = async () => {
            try {
              setPublishLoading(true);
              const newStatus = status === 'unpublish' ? 'published' : 'unpublish';
              const response = await axios.patch(`${apiBaseUrl}/api/toggle-publish/${id}`, {
                id,
                typeAction: 'update',
                status,
                idInput: id,
                stock_id: row.original.stock_id,
                prefix: row.original.prefix,
                model: row.original.model,
                summary_result: row.original.content,
                note: row.original.note,
              });

              if (response.status === 200) {
                setData((prevData) =>
                  prevData.map((item) => (item.id === id ? { ...item, status: newStatus } : item)),
                );
              } else {
                console.error('Failed to toggle publish status:', response);
              }
            } catch (error) {
              console.error('Error toggling publish status:', error);
            } finally {
              setPublishLoading(false);
            }
          };

          const handleEditQuestion = async (data: any) => {
            setLoading(true);
            try {
              const response = await axios.patch(`${apiBaseUrl}/api/update-summary`, {
                note: data.note,
                text_summary: data.content,
                id,
              });

              if (response.status === 200) {
                await fetchData(currentPage);
                setOpenPopup(false);
              } else {
                console.error('Failed to update summary:', response);
              }
            } catch (error) {
              console.error('Error updating summary:', error);
            }
            return setLoading(false);
          };

          return (
            <div className="flex items-center justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFullContent(true)}
                className="px-2 py-0.5 h-7"
              >
                <Eye className="h-4 w-4" />
              </Button>

              <Dialog open={showFullContent} onOpenChange={setShowFullContent}>
                <DialogContent className="max-w-3xl max-h-[80vh]">
                  <DialogHeader>
                    <DialogTitle>Nội dung chi tiết</DialogTitle>
                  </DialogHeader>
                  <div className="mt-4 overflow-y-auto max-h-[60vh]">
                    <div className="whitespace-pre-wrap text-[16px] leading-relaxed">{content}</div>
                  </div>
                </DialogContent>
              </Dialog>

              <Dialog
                open={openPopup}
                onOpenChange={(bool) => {
                  setOpenPopup(bool);
                  if (!bool) {
                    reset();
                  } else {
                    reset({
                      content: row.original.content,
                      note: row.original.note,
                    });
                  }
                }}
              >
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOpenPopup(true)}
                    className="px-2 py-0.5 h-7"
                  >
                    Sửa
                  </Button>
                </DialogTrigger>
                <DialogContent className="rounded-lg sm:px-3 h-[calc(100dvh-200px)] min-w-[calc(100dvw-112px)]">
                  <DialogHeader className="flex flex-col gap-3">
                    <DialogTitle className="max-sm:hidden text-center">Sửa summary</DialogTitle>
                    <form
                      className="flex flex-col gap-3 h-full justify-between"
                      onSubmit={handleSubmit(handleEditQuestion)}
                    >
                      <div className="flex flex-col gap-2">
                        <div className="flex flex-col items-start">
                          <span>Nội dung:</span>
                          <Textarea
                            placeholder="Nội dung"
                            className="w-full input-scroll-bar"
                            defaultValue={(row.original as any).answer || ''}
                            rows={10}
                            {...register('content')}
                          />
                        </div>
                        <div className="flex flex-col items-start">
                          <span>Ghi chú:</span>
                          <Textarea
                            placeholder="Nội dung"
                            className="w-full input-scroll-bar"
                            defaultValue={(row.original as any).note || ''}
                            rows={1}
                            {...register('note')}
                          />
                        </div>
                      </div>
                      <div className="flex justify-end">
                        <Button disabled={loading} type="submit" variant="default">
                          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Ok'}
                        </Button>
                      </div>
                    </form>
                  </DialogHeader>
                </DialogContent>
              </Dialog>

              <Button
                variant="outline"
                size="sm"
                onClick={handleTogglePublish}
                disabled={publishLoading}
                className="w-[70px] px-2 py-0.5 h-7"
              >
                {publishLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : status === 'unpublish' ? (
                  'Publish'
                ) : (
                  'Unpublish'
                )}
              </Button>
            </div>
          );
        },
      },
    ];

    return baseColumns;
  }, []);

  const table = useReactTable({
    data: data,
    columns,
    state: {
      columnFilters,
    },
    enableRowSelection: true,
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="space-y-4 s3-explr-container">
      <div className="flex items-center space-x-4 s3-explr-toolbar">
        <div className="relative max-w-sm">
          <Input
            placeholder="Tìm kiếm theo mã..."
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.target.value)}
            className="pr-8"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => setSearchQuery('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      <div className="rounded-md border s3-explr-table-container">
        <Table className="s3-explr-table">
          <TableHeader className="s3-explr-table-header">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent s3-explr-header-row">
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="s3-explr-header-cell">
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="s3-explr-table-body">
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Đang tải dữ liệu...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows?.map((row) => (
                <TableRow key={row.id} className="h-12 s3-explr-row">
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="align-middle s3-explr-cell">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Không có kết quả.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="mt-4">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {Array.from({ length: totalPages }, (_, i) => i + 1)
              .filter(
                (page) => page === 1 || page === totalPages || Math.abs(page - currentPage) <= 1,
              )
              .map((page, index, array) => (
                <>
                  {index > 0 && array[index - 1] !== page - 1 && (
                    <PaginationItem key={page + '...'}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                      className={`cursor-pointer ${isLoading ? 'pointer-events-none' : ''}`}
                    >
                      {loadingPage === page ? <Loader2 className="h-4 w-4 animate-spin" /> : page}
                    </PaginationLink>
                  </PaginationItem>
                </>
              ))}

            <PaginationItem>
              <PaginationNext
                onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                className={
                  currentPage === totalPages || loadingPage !== null
                    ? 'pointer-events-none opacity-50'
                    : 'cursor-pointer'
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

import { PublicClientApplication } from '@azure/msal-browser';
import {
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ArrowUpTrayIcon,
  CircleStackIcon,
  FolderIcon,
  FolderPlusIcon,
  GlobeAltIcon,
  PencilIcon,
  PlusIcon,
  ShareIcon,
  StopIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import * as Modal from '@radix-ui/react-dialog';
import { GoogleOAuthProvider, useGoogleLogin } from '@react-oauth/google';
import {
  ColumnDef,
  SortingState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import axios from 'axios';
import axiosRetry from 'axios-retry';
import dayjs from 'dayjs';
import { findIndex, forEach } from 'lodash';
import { ArrowUpDown, Loader2, MoveUpRight, PenOff } from 'lucide-react';
import {
  Dispatch,
  MutableRefObject,
  SetStateAction,
  memo,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { toast as hotToast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { isDisableField, isTextarea, keyMapMetadata } from '@/common/utils';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Toaster } from '@/components/ui/toaster';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import SearchBox from '@/components/utils/SearchBox.tsx';
import { useToast } from '@/hooks/use-toast.ts';
import { getUserInfoFromLocal } from '@/lib/utils.ts';

import FileUpload from '../components/utils/DragDrop';
import DialogRemoveFiles from './Dialog/DialogRemoveFiles';
import DialogShare from './Dialog/DialogShare';

const PICKER_BUILDER_DEVELOPER_KEY = 'AIzaSyBI8KiTWudkw8iR5cjH7ZsA4JBN1NqJw8A';
const CLIENT_ID = '726815747852-5deq413t8kd8j3mgj48d46eatjauapq5.apps.googleusercontent.com';
const SCOPES = 'https://www.googleapis.com/auth/drive';

declare global {
  interface Window {
    gapi: any;
    google: any;
  }
}

type OneDriveFile = {
  id: string;
  name: string;
  size: number;
  lastModified: Date;
  contentType?: string;
  isFolder: boolean;
  parentId?: string;
};

type SharePointFile = {
  id: string;
  name: string;
  size: number;
  lastModified: Date;
  contentType?: string;
  isFolder: boolean;
  parentId?: string;
};

type SharePointSite = {
  id: string;
  name: string;
  description?: string;
  hostname?: string;
  sitePath?: string;
};

axiosRetry(axios, {
  retries: 3,
  shouldResetTimeout: true,
  retryCondition: (error: any) => {
    return axiosRetry.isRetryableError(error) && error.config.method === 'get';
  },
});

type S3File = {
  key: string;
  name: string;
  size: number;
  lastModified: Date;
  contentType?: string;
  isFolder: boolean;
  etag?: string;
};

interface SyncJobStatus {
  status: string;
  created_date: string;
  dataSourceId: string;
  ingestionJobId: string;
  knowledgeBaseId: string;
  user_name: string;
  user_id: string;
  is_still_in_stock: boolean;
  errorMessage?: string;
  statistics?: {
    numberOfDocumentsDeleted: number;
    numberOfDocumentsFailed: number;
    numberOfDocumentsScanned: number;
    numberOfMetadataDocumentsModified: number;
    numberOfMetadataDocumentsScanned: number;
    numberOfModifiedDocumentsIndexed: number;
    numberOfNewDocumentsIndexed: number;
  };
}

const ignoreCache = {
  current: false,
};

const CRAWL_JOBS_KEY = 'crawl:jobs';
const CURRENT_CRAWL_JOB_KEY = 'crawl:current_job';

type CrawlJobStatus = {
  success: boolean;
  status: 'scraping' | 'completed' | 'failed';
  completed: number;
  total: number;
  creditsUsed: number;
  expiresAt: string;
  startTime?: number;
  isProcessing?: boolean;
};

const useCurrentCrawlJob = (apiBaseUrl: string) => {
  const [currentJob, setCurrentJob] = useState<{
    id: string;
    url: string;
    limit: number;
    prefix: string;
    startTime?: number;
    status?: CrawlJobStatus;
    taskId: string;
  } | null>(null);
  const [elapsedTime, setElapsedTime] = useState<number>(0);

  // Load current job from localStorage on mount
  useEffect(() => {
    const savedJob = localStorage.getItem(CURRENT_CRAWL_JOB_KEY);
    if (savedJob) {
      const parsedJob = JSON.parse(savedJob) as typeof currentJob;
      setCurrentJob(parsedJob);

      // Initialize elapsed time from the job's start time
      if (parsedJob?.startTime) {
        setElapsedTime(Math.floor((Date.now() - parsedJob!.startTime) / 1000));
      }
    }
  }, []);

  // Update elapsed time every second while job exists
  useEffect(() => {
    if (!currentJob?.startTime) return;

    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - currentJob.startTime!) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [currentJob?.startTime]);

  // Poll for job status
  useEffect(() => {
    if (!currentJob?.id) return;

    const pollStatus = async () => {
      try {
        const response = await axios.get(
          `${apiBaseUrl}/api/crawl/${currentJob.id}?taskId=${currentJob.taskId}`,
        );
        const status = response.data as CrawlJobStatus;

        setCurrentJob((prev) => {
          if (!prev) return null;
          const updated = { ...prev, status };

          // Update localStorage with latest status
          localStorage.setItem(CURRENT_CRAWL_JOB_KEY, JSON.stringify(updated));

          // If job is completed or failed, return the updated state one last time
          // This ensures the completion/failure notification is triggered
          if (status.status === 'completed' || status.status === 'failed') {
            // Use setTimeout to clear the job after the status update is processed
            setTimeout(() => {
              setCurrentJob(null);
              localStorage.removeItem(CURRENT_CRAWL_JOB_KEY);
            }, 100);
          }

          return updated;
        });
      } catch (error) {
        console.error('Error polling job status:', error);
      }
    };

    // Poll every 3 seconds
    const interval = setInterval(pollStatus, 3000);
    void pollStatus(); // Initial poll

    return () => clearInterval(interval);
  }, [currentJob?.id, apiBaseUrl]);

  return {
    currentJob,
    elapsedTime,
    setCurrentJob: (job: typeof currentJob) => {
      if (job && !job.startTime) {
        job.startTime = Date.now();
      }
      setCurrentJob(job);
      if (job) {
        localStorage.setItem(CURRENT_CRAWL_JOB_KEY, JSON.stringify(job));
      } else {
        localStorage.removeItem(CURRENT_CRAWL_JOB_KEY);
      }
    },
  };
};

const getAllFilesUnderFolder = (fileSystem: Record<string, S3File[]>, prefix: string): string[] => {
  const files: string[] = [];
  const searchInPrefix = (currentPrefix: string) => {
    const currentFiles = fileSystem[currentPrefix] || [];
    currentFiles.forEach((file) => {
      files.push(file.key);
      if (file.isFolder) {
        searchInPrefix(file.key);
      }
    });
  };
  searchInPrefix(prefix);
  return files;
};

const getAllFilesInCurrentView = (
  fileSystem: Record<string, S3File[]>,
  prefix: string,
): string[] => {
  const files: string[] = [];
  // Add current level files
  const currentFiles = fileSystem[prefix] || [];
  currentFiles.forEach((file) => {
    files.push(file.key);
    // If it's a folder, add all files under it
    if (file.isFolder) {
      files.push(...getAllFilesUnderFolder(fileSystem, file.key));
    }
  });
  return files;
};

const useFiles = (
  apiBaseUrl: string,
  rootPrefix: string,
  displayPrefixes: string[] = [],
  searchQuery: string,
  currentPage: number,
  setSearchQuery: Dispatch<SetStateAction<string>>,
  idCreator: string | undefined,
  selectedKeys?: Set<string>,
  selectStates?: MutableRefObject<Record<string, boolean>>,
  setSelectedKeys?: any,
) => {
  const [data, setData] = useState<Record<string, S3File[]>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [estimatedTotalHits, setEstimatedTotalHits] = useState(0);

  const rootPrefixRef = useRef(rootPrefix);
  useEffect(() => {
    rootPrefixRef.current = rootPrefix;
  }, [rootPrefix]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(`${apiBaseUrl}/api/files`, {
        params: {
          cache: false,
          displayPrefixes: JSON.stringify(displayPrefixes),
          prefix: rootPrefixRef.current,
          excludePrivateFiles: idCreator ? 'false' : 'true',
        },
        timeout: 300000,
      });

      const newSelectedKeys: string[] = [];

      // console.log('selectedKeys', selectedKeys);

      forEach(response.data, (files, folderKey) => {
        if (selectedKeys?.has(folderKey)) {
          forEach(files, (file) => {
            if (!(selectStates?.current[file.key] === false)) {
              newSelectedKeys.push(file.key);
            }
          });
        }
      });

      // console.log('newSelectedKeys', newSelectedKeys);

      if (newSelectedKeys.length) {
        setSelectedKeys?.((prev: any) => {
          const merged = new Set(prev);
          newSelectedKeys.forEach((key) => merged.add(key));
          return merged;
        });
      }

      setData(response.data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch files'));
    } finally {
      setIsLoading(false);
      setEstimatedTotalHits(0);
      setSearchQuery('');
    }
  };

  const handleSearch = async (query: string) => {
    try {
      if (!query) {
        return;
      }
      setIsLoading(true);
      const response: any = await axios.get(
        `${apiBaseUrl}/api/search-db?query=${query}&rootPrefix=${rootPrefix}&offset=${(currentPage - 1) * 20}&limit=20`,
        {
          timeout: 300000,
        },
      );
      setData({ [rootPrefix]: response?.data?.result || [] });
      setEstimatedTotalHits(response?.data?.estimatedTotalHits || 0);
      return response;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch files'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!searchQuery && !isLoading) {
      void fetchData();
    }

    if (searchQuery && !isLoading) {
      void handleSearch(searchQuery);
    }
  }, [searchQuery, currentPage]);

  return {
    data,
    isLoading,
    error,
    estimatedTotalHits,
    refetch: fetchData,
  };
};

// Add isTextFile function
const isTextFile = (file: S3File): boolean => {
  const extension = file?.name?.split('.')?.pop()?.toLowerCase() ?? '';
  const textExtensions = [
    'txt',
    'md',
    'json',
    'js',
    'ts',
    'jsx',
    'tsx',
    'css',
    'html',
    'xml',
    'yaml',
    'yml',
  ];
  return textExtensions.includes(extension || '');
};

const msalConfig = {
  auth: {
    clientId: '584d5fa9-5123-4602-a1a5-a3746a83745f',
    authority: 'https://login.microsoftonline.com/consumers',
    redirectUri:
      import.meta.env['VITE_ONE_DRIVE_REDIRECT_URI'] ||
      'https://s3-explorer.cmcts1.studio.ai.vn/sharepoint/redirect',
  },
};

const msalInstance = new PublicClientApplication(msalConfig);

const HomeWithoutContext = memo<{
  homeLabel?: string;
  apiBaseUrl?: string;
  rootUrlHostApi?: string;
  rootPrefix?: string;
  asSelector?: boolean;
  onSelected?: (selectedFiles: S3File[]) => void;
  displayPrefixes?: string[];
  preSelectedFiles?: string[];
  idCreator?: string;
}>(
  ({
    homeLabel = 'Kho tài liệu',
    apiBaseUrl = import.meta.env['VITE_APP_API_URL'],
    rootPrefix = '',
    rootUrlHostApi,
    asSelector = false,
    onSelected = (files) => console.log('Đã chọn:', files),
    displayPrefixes,
    preSelectedFiles,
    idCreator,
  }) => {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set(preSelectedFiles));
    const [currentPrefix, setCurrentPrefix] = useState(rootPrefix);
    const [forcePrefix, setForcePrefix] = useState<string | null>('');
    const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
    const [newFolderName, setNewFolderName] = useState('');
    const [isCreatingFolder, setIsCreatingFolder] = useState(false);
    const [fileToDelete, setFileToDelete] = useState<S3File | null>(null);
    const [fileToMove, setFileToMove] = useState<S3File | null>(null);
    const [isBulkDeleting, setIsBulkDeleting] = useState(false);
    const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
    const [, setUploadType] = useState<'files' | 'folder' | null>(null);
    const searchBoxRef = useRef<any>(null);
    const { toast } = useToast();
    const [fileToRename, setFileToRename] = useState<S3File | null>(null);
    const [metadataForEdit, setMetadataForEdit] = useState<any>(null);
    const [newName, setNewName] = useState('');
    const [openDialogRename, setOpenDialogRename] = useState(false);
    const [isRenaming, setIsRenaming] = useState(false);
    const [downloadingItems, setDownloadingItems] = useState<Set<string>>(new Set());
    const [fileContent, setFileContent] = useState<string>('');
    const [isLoadingContent, setIsLoadingContent] = useState(false);
    const [isSavingContent, setIsSavingContent] = useState(false);
    const [activeTab, setActiveTab] = useState<'rename' | 'edit' | 'edit-metadata'>('rename');
    const [isWebsiteDialogOpen, setIsWebsiteDialogOpen] = useState(false);
    const [websiteUrl, setWebsiteUrl] = useState('');
    const [crawlLimit, setCrawlLimit] = useState('1');
    const [isCrawling, setIsCrawling] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [_isUploading, setIsUploading] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [isFolderClicking, setIsFolderClicking] = useState(false);
    const { currentJob, elapsedTime, setCurrentJob } = useCurrentCrawlJob(apiBaseUrl);
    const [duplicateFiles, setDuplicateFiles] = useState<FileList | null>(null);
    const [folderExist, setFolderExist] = useState<string>('');
    const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
    const [folderToShowFlow, setFolderToShowFlow] = useState('');
    const [showInfoFlowsDialogs, setShowInfoFlowsDialogs] = useState(false);
    const [loadingFlowsDialogs, setLoadingFlowsDialogs] = useState(false);
    const [dataFlowModels, setDataFlowModels] = useState([]);
    const [isLoadingHandleExportReport, setIsLoadingHandleExportReport] = useState(false);
    const [rootFolderName, setRootFolderName] = useState('');
    const selectStates = useRef<Record<string, boolean>>({});
    const [fileToShare, setFileToShare] = useState<S3File | null>(null);

    const handleGetInfoFlows = async (folderName: string) => {
      setLoadingFlowsDialogs(true);
      try {
        const dataResponse = await axios.get(
          `${rootUrlHostApi}get-knowledge-base/knowledge-base-name/${folderName}`,
          {
            headers: {
              Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAyOTliMmIwLTk4MGEtNGQ1Yy04NjkyLWJjNjllM2I1NzZlMiIsInVzZXJuYW1lIjoiY21jX3RzX2FkbWluIiwiaWF0IjoxNzQ3MTk0MTg3fQ.7LB2rgd0fQnhZP_cFgTTCPMSYl0_apwffcNEd7oqjeM`,
            },
          },
        );
        if (dataResponse.status === 200) {
          setDataFlowModels(
            dataResponse.data.knowledgeBase.chatflows.map((item: any) => ({
              ...item,
              KnowledgeBaseId: dataResponse.data.knowledgeBase.knowledge_base_id,
            })),
          );
        }
      } catch (error) {
      } finally {
        setLoadingFlowsDialogs(false);
      }
    };

    useEffect(() => {
      if (folderToShowFlow && rootUrlHostApi) {
        handleGetInfoFlows(folderToShowFlow);
      }
    }, [folderToShowFlow]);

    useEffect(() => {
      console.log('pre selected files', preSelectedFiles);
    }, [preSelectedFiles]);

    const [syncJobStatus, setSyncJobStatus] = useState<SyncJobStatus | null>(null);

    const [_isMsalReady, setIsMsalReady] = useState(false);
    const [_isAuthenticated, setIsAuthenticated] = useState(false);
    const [showOneDriveModal, setShowOneDriveModal] = useState(false);
    const [folders, setFolders] = useState<OneDriveFile[]>([]);
    const [files, setFiles] = useState<OneDriveFile[]>([]);
    const [breadcrumbOneDrive, setBreadcrumbOneDrive] = useState<{ id: string; name: string }[]>([
      { id: 'root', name: 'OneDrive' },
    ]);
    const [isLoadingOneDrive, setIsLoadingOneDrive] = useState(false);
    const [selectedOneDriveItems, setSelectedOneDriveItems] = useState<OneDriveFile[]>([]);
    const [isLoadingSharePoint, setIsLoadingSharePoint] = useState(false);
    const [sharePointFiles, setSharePointFiles] = useState<SharePointFile[]>([]);
    const [sharePointFolders, setSharePointFolders] = useState<SharePointFile[]>([]);
    const [selectedSharePointItems, setSelectedSharePointItems] = useState<SharePointFile[]>([]);
    const [sharePointBreadcrumbs, setSharePointBreadcrumbs] = useState<
      { id: string; name: string }[]
    >([{ id: 'root', name: 'SharePoint' }]);
    const [isSharePointModalOpen, setIsSharePointModalOpen] = useState(false);
    const [sharePointSites, setSharePointSites] = useState<SharePointSite[]>([]);
    const [selectedSite, setSelectedSite] = useState<SharePointSite | null>(null);
    const [isSharePointLogoutConfirmOpen, setIsSharePointLogoutConfirmOpen] = useState(false);
    const [isOneDriveLogoutConfirmOpen, setIsOneDriveLogoutConfirmOpen] = useState(false);

    const url = new URL(window.location.href);
    const prefixParam = url.searchParams.get('prefix') || '';
    const effectivePrefix = prefixParam || forcePrefix || rootPrefix;

    const [_token, setToken] = useState<string | null>(null);
    const {
      data: fileSystem,
      isLoading,
      error,
      estimatedTotalHits,
      refetch: refreshFiles,
    } = useFiles(
      apiBaseUrl,
      effectivePrefix,
      displayPrefixes,
      searchQuery,
      currentPage,
      setSearchQuery,
      idCreator,
      selectedKeys,
      selectStates,
      setSelectedKeys,
    );

    const [showAddFileMenu, setShowAddFileMenu] = useState(false);
    const addFileMenuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (!showAddFileMenu) return;
      function handleClickOutside(event: MouseEvent) {
        if (addFileMenuRef.current && !addFileMenuRef.current.contains(event.target as Node)) {
          setShowAddFileMenu(false);
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showAddFileMenu]);

    const loginSharePoint = async () => {
      try {
        const response = await axios.get(`${apiBaseUrl}/api/login-sharepoint`);
        const { loginUrl } = response.data;

        const popup = window.open(loginUrl, 'SharePoint Login', 'width=600,height=600');
        if (!popup) throw new Error('Không thể mở cửa sổ pop-up');
        popup.focus();

        const interval = setInterval(() => {
          try {
            if (popup.closed) {
              clearInterval(interval);
              return;
            }

            const currentUrl = popup.location.href;
            if (currentUrl.includes('code=')) {
              const authorizationCode = new URL(currentUrl).searchParams.get('code');
              if (authorizationCode) {
                fetchSharePointAccessToken(authorizationCode); // Hàm này nên lưu access token vào localStorage
                popup.close();
                clearInterval(interval);
              }
            }
          } catch (err: unknown) {
            if (err instanceof Error && !err.message.includes('cross-origin')) {
              console.error('SharePoint login error:', err);
            }
          }
        }, 1000);
      } catch (err) {
        hotToast.error(
          'Lỗi khi đăng nhập SharePoint: ' + (err instanceof Error ? err.message : 'Unknown error'),
        );
      }
    };

    const logoutSharePoint = async () => {
      try {
        setSharePointFiles([]);
        setSharePointBreadcrumbs([{ id: 'root', name: 'SharePoint' }]);
        window.location.href =
          'https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri=' +
          encodeURIComponent(window.location.origin);
        toast({
          title: 'Success',
          description: 'Logged out from SharePoint',
        });
      } catch (error) {
        console.error('Error logging out from SharePoint:', error);
        toast({
          title: 'Error',
          description: 'Failed to logout from SharePoint',
          variant: 'destructive',
        });
      }
    };

    const fetchSharePointAccessToken = async (authorizationCode: string) => {
      try {
        const response = await axios.post(`${apiBaseUrl}/api/get-sharepoint-access-token`, {
          code: authorizationCode,
          userId: '1',
        });

        const { accessToken } = response.data;

        localStorage.setItem('sharepoint_access_token', accessToken);
        setIsAuthenticated(true);
        setIsSharePointModalOpen(true);
      } catch (error) {
        console.error('❌ Error fetching SharePoint access token:', error);
        toast({
          title: 'Error',
          description: 'Failed to connect to SharePoint',
          variant: 'destructive',
        });
      }
    };

    const fetchSharePointSites = async () => {
      try {
        setIsLoadingSharePoint(true);
        const response = await axios.post(`${apiBaseUrl}/api/list-site-sharepoint`, {
          userId: '1',
        });
        const data = response.data.sites.map((item: any): SharePointSite => {
          const url = new URL(item.webUrl);
          return {
            id: item.id,
            name: item.name,
            description: item.description,
            hostname: item.hostname || url.hostname,
            sitePath: item.sitePath || url.pathname.replace(/^\/+/, ''),
          };
        });
        console.log('data', data);
        setSharePointSites(data);
        setIsLoadingSharePoint(false);
      } catch (error) {
        console.error('❌ Error fetching SharePoint sites:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch SharePoint sites',
          variant: 'destructive',
        });
        setIsLoadingSharePoint(false);
      }
    };

    const handleSelectSite = async (site: SharePointSite) => {
      setSelectedSite(site);
      console.log(site);
      await fetchSharePointFiles('root', site.name, site.hostname || '', site.sitePath || '');
    };

    const fetchSharePointFiles = async (
      folderId: string,
      folderName: string,
      hostname: string,
      sitePath: string,
    ) => {
      try {
        setIsLoadingSharePoint(true);

        const response = await axios.post(`${apiBaseUrl}/api/list-file-sharepoint`, {
          folderId,
          userId: '1',
          hostname,
          sitePath,
        });

        const data = response.data.files.map(
          (item: any): SharePointFile => ({
            id: item.id,
            name: item.name,
            size: item.size ?? 0,
            lastModified: item.lastModifiedDateTime
              ? new Date(item.lastModifiedDateTime)
              : new Date(),
            contentType: item.mimeType ?? undefined,
            isFolder: item.type === 'folder',
            parentId: item.parentId ?? '',
          }),
        );

        setSharePointFolders(data.filter((item: SharePointFile) => item.isFolder));
        setSharePointFiles(data.filter((item: SharePointFile) => !item.isFolder));

        setIsSharePointModalOpen(true);

        setSharePointBreadcrumbs((prev) => {
          if (prev.some((b) => b.id === folderId)) return prev;
          return [
            ...prev,
            { id: folderId, name: folderName === 'root' ? 'SharePoint' : folderName },
          ];
        });

        setIsLoadingSharePoint(false);
      } catch (error) {
        console.error('❌ Error fetching SharePoint files:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch SharePoint files',
          variant: 'destructive',
        });
        setIsLoadingSharePoint(false);
      }
    };

    const handleSharePointSelectItem = (item: SharePointFile) => {
      setSelectedSharePointItems((prevItems) => {
        const exists = prevItems.some((selectedItem) => selectedItem.id === item.id);
        if (exists) {
          return prevItems.filter((selectedItem) => selectedItem.id !== item.id);
        } else {
          return [
            ...prevItems,
            {
              id: item.id,
              name: item.name,
              size: item.size,
              lastModified: item.lastModified,
              contentType: item.contentType,
              isFolder: item.isFolder,
              parentId: item.parentId,
            },
          ];
        }
      });
    };

    const handleSharePointFolderNavigation = (index: number) => {
      setSharePointBreadcrumbs((prev) => prev.slice(0, index + 1));
      const folderId = sharePointBreadcrumbs[index]?.id;
      if (folderId) {
        fetchSharePointFiles(
          folderId,
          'SharePoint',
          selectedSite?.hostname || '',
          selectedSite?.sitePath || '',
        );
      }
    };

    const syncSharePointToS3 = async (
      selectedItems: SharePointFile[],
      prefix: string,
    ): Promise<void> => {
      const accessToken = localStorage.getItem('sharepoint_access_token');
      if (!accessToken) {
        hotToast.error('Không tìm thấy access token. Vui lòng đăng nhập lại.');
        return;
      }

      const toastId = hotToast.loading('Đang đồng bộ SharePoint...', {
        position: 'bottom-right',
        autoClose: false,
      });

      try {
        const payload = {
          prefix,
          folders: [] as { id: string; name: string }[],
          files: [] as { id: string; name: string }[],
          hostname: selectedSite?.hostname || '',
          sitePath: selectedSite?.sitePath || '',
        };

        selectedItems.forEach((item) => {
          if (item.isFolder) {
            payload.folders.push({ id: item.id, name: item.name });
          } else {
            payload.files.push({ id: item.id, name: item.name });
          }
        });

        const response = await axios.post(`${apiBaseUrl}/api/sync-sharepoint-to-s3`, payload, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });

        void refreshFiles();
        hotToast.update(toastId, {
          render: `Đồng bộ thành công! Đã tải lên ${response.data.uploadedKeys.length} file.`,
          type: 'success',
          isLoading: false,
          autoClose: 3000,
          position: 'bottom-right',
        });

        setIsSharePointModalOpen(false);
      } catch (error) {
        console.error('Lỗi khi đồng bộ file từ SharePoint:', error);
        hotToast.update(toastId, {
          render: 'Không thể đồng bộ file từ SharePoint.',
          type: 'error',
          isLoading: false,
          autoClose: 3000,
          position: 'bottom-right',
        });
      }
    };

    const handleCloseSharePointModal = () => {
      setIsSharePointModalOpen(false);
      setSharePointFiles([]);
      setSelectedSharePointItems([]);
      setSharePointBreadcrumbs([{ id: 'root', name: 'SharePoint' }]);
    };

    useEffect(() => {
      if (isSharePointModalOpen) {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        if (code) {
          fetchSharePointAccessToken(code);
          window.history.replaceState({}, document.title, window.location.pathname);
        }
      }
    }, [isSharePointModalOpen]);

    useEffect(() => {
      msalInstance
        .initialize()
        .then(() => {
          setIsMsalReady(true);
        })
        .catch((error) => console.error('❌ Lỗi khởi tạo MSAL:', error));
    }, []);

    useEffect(() => {
      checkLoginStatus();
    }, []);

    async function checkLoginStatus() {
      const token = localStorage.getItem('onedrive_access_token');
      if (token) {
        setIsAuthenticated(true);
      }
    }

    const handleOneDriveSelectItem = (item: OneDriveFile) => {
      setSelectedOneDriveItems((prevItems) => {
        const exists = prevItems.some((selectedItem) => selectedItem.id === item.id);
        if (exists) {
          return prevItems.filter((selectedItem) => selectedItem.id !== item.id);
        } else {
          return [
            ...prevItems,
            {
              id: item.id,
              name: item.name,
              size: item.size,
              lastModified: item.lastModified,
              contentType: item.contentType,
              isFolder: item.isFolder,
              parentId: item.parentId,
            },
          ];
        }
      });
    };

    function formatBytes(bytes: number): string {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    function formatDate(isoString: string): string {
      const date = new Date(isoString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    const loginOneDrive = async () => {
      try {
        const response = await axios.get(`${apiBaseUrl}/api/login-one-drive`);
        const { loginUrl } = response.data;

        const popup = window.open(loginUrl, 'OneDrive Login', 'width=600,height=600');
        if (!popup) throw new Error('Không thể mở cửa sổ pop-up');
        popup.focus();

        const interval = setInterval(() => {
          try {
            if (popup.closed) {
              clearInterval(interval);
              return;
            }
            const currentUrl = popup.location.href;
            if (currentUrl.includes('code=')) {
              const authorizationCode = new URL(currentUrl).searchParams.get('code');
              if (authorizationCode) {
                fetchAccessToken(authorizationCode);
                popup.close();
                clearInterval(interval);
              }
            }
          } catch (err: unknown) {
            // Ignore cross-origin errors
            if (err instanceof Error && !err.message.includes('cross-origin')) {
              console.error('OneDrive login error:', err);
            }
          }
        }, 1000);
      } catch (err) {
        hotToast.error(
          'Lỗi khi đăng nhập OneDrive: ' + (err instanceof Error ? err.message : 'Unknown error'),
        );
      }
    };

    const logoutOneDrive = async () => {
      try {
        await axios.post(`${apiBaseUrl}/api/logout-one-drive`);
        localStorage.removeItem('onedrive_access_token');

        hotToast.success('Đã đăng xuất khỏi OneDrive');

        window.location.href =
          'https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri=' +
          encodeURIComponent(window.location.origin);
      } catch (err) {
        hotToast.error(
          'Lỗi khi đăng xuất OneDrive: ' + (err instanceof Error ? err.message : 'Unknown error'),
        );
      }
    };

    const fetchAccessToken = async (authorizationCode: string) => {
      try {
        const userId = '1';
        const response = await axios.post(`${apiBaseUrl}/api/get-one-drive-access-token`, {
          authorizationCode,
          userId,
        });

        const { accessToken } = response.data;

        localStorage.setItem('onedrive_access_token', accessToken);
        setIsAuthenticated(true);
        setShowOneDriveModal(true);
        await fetchOneDriveFiles('root', 'One Drive');
      } catch (error) {
        console.error('❌ Lỗi khi lấy Access Token:', error);
      }
    };

    const fetchOneDriveFiles = async (folderId: string, folderName: string) => {
      try {
        setIsLoadingOneDrive(true);
        const userId = '1';
        const response = await axios.post(`${apiBaseUrl}/api/list-file-one-drive`, {
          folderId,
          userId,
        });

        const data = response.data.map(
          (item: any): OneDriveFile => ({
            id: item.id,
            name: item.name,
            size: item.size ?? 0,
            lastModified: item.lastModifiedDateTime
              ? new Date(item.lastModifiedDateTime)
              : new Date(),
            contentType: item.mimeType ?? undefined,
            isFolder: item.type === 'folder',
            parentId: item.parentId ?? '',
          }),
        );

        setFolders(data.filter((item: OneDriveFile) => item.isFolder));
        setFiles(data.filter((item: OneDriveFile) => !item.isFolder));

        setShowOneDriveModal(true);
        setBreadcrumbOneDrive((prev) => {
          if (prev.some((b) => b.id === folderId)) return prev;
          return [...prev, { id: folderId, name: folderName === 'root' ? 'OneDrive' : folderName }];
        });

        setIsLoadingOneDrive(false);
      } catch (error) {
        console.error('Lỗi lấy danh sách file:', error);
        setIsLoadingOneDrive(false);
      }
    };

    const handleOneDriveFolderNavigation = (index: number) => {
      if (index === breadcrumbOneDrive.length - 1) return;

      const targetFolder = breadcrumbOneDrive[index];
      if (!targetFolder) return;

      setBreadcrumbOneDrive(breadcrumbOneDrive.slice(0, index + 1));
      fetchOneDriveFiles(targetFolder.id, targetFolder.name);
    };

    const syncOneDriveToS3 = async (
      selectedItems: OneDriveFile[],
      prefix: string,
    ): Promise<void> => {
      const accessToken = localStorage.getItem('onedrive_access_token');
      if (!accessToken) {
        hotToast.error('Không tìm thấy access token. Vui lòng đăng nhập lại.');
        return;
      }

      const toastId = hotToast.loading('Đang đồng bộ OneDrive...', {
        position: 'bottom-right',
        autoClose: false,
      });

      try {
        const payload = {
          prefix,
          folders: [] as { id: string; name: string }[],
          files: [] as { id: string; name: string }[],
        };

        selectedItems.forEach((item) => {
          if (item.isFolder) {
            payload.folders.push({ id: item.id, name: item.name });
          } else {
            payload.files.push({ id: item.id, name: item.name });
          }
        });

        const response = await axios.post(`${apiBaseUrl}/api/sync-one-drive-to-s3`, payload, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });

        void refreshFiles();
        hotToast.update(toastId, {
          render: `Đồng bộ thành công! Đã tải lên ${response.data.uploadedKeys.length} file.`,
          type: 'success',
          isLoading: false,
          autoClose: 3000,
          position: 'bottom-right',
        });
      } catch (error) {
        console.error('Lỗi khi đồng bộ file từ OneDrive:', error);

        hotToast.update(toastId, {
          render: 'Không thể đồng bộ file từ OneDrive.',
          type: 'error',
          isLoading: false,
          autoClose: 3000,
          position: 'bottom-right',
        });
      }
    };

    const handleCloseOneDriveModal = () => {
      setShowOneDriveModal(false);
      setBreadcrumbOneDrive([{ id: 'root', name: 'OneDrive' }]);
      setFolders([]);
      setFiles([]);
      setSelectedOneDriveItems([]);
      setIsLoadingOneDrive(false);
    };

    const login = useGoogleLogin({
      onSuccess: (tokenResponse) => {
        setToken(tokenResponse.access_token);
        localStorage.setItem('google_access_token', tokenResponse.access_token);
        loadGooglePicker(tokenResponse.access_token);
      },
      onError: () => {
        console.log('Đăng nhập thất bại!');
      },
      scope: SCOPES,
    });

    const loadGooglePicker = (accessToken: string) => {
      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = () => {
        window.gapi.load('picker', () => initializePicker(accessToken));
      };
      document.body.appendChild(script);
    };

    const initializePicker = (accessToken: string) => {
      const docsView = new window.google.picker.DocsView(window.google.picker.ViewId.DOCS)
        .setIncludeFolders(true)
        .setSelectFolderEnabled(true)
        .setOwnedByMe(false);

      const driveView = new window.google.picker.DocsView(window.google.picker.ViewId.DRIVE)
        .setIncludeFolders(true)
        .setSelectFolderEnabled(true);

      const picker = new window.google.picker.PickerBuilder()
        .setDeveloperKey(PICKER_BUILDER_DEVELOPER_KEY)
        .setOAuthToken(accessToken)
        .enableFeature(window.google.picker.Feature.MULTISELECT_ENABLED) // Chọn nhiều file
        .setLocale('vi')
        .addView(docsView)
        .addView(driveView)
        .setCallback(async (data: any) => {
          if (data.action === window.google.picker.Action.PICKED) {
            const selectedFiles = data.docs.map((doc: any) => ({
              id: doc.id,
              name: doc.name,
              mimeType: doc.mimeType,
            }));

            await syncDriveToS3(selectedFiles, currentPrefix);
          }
        })
        .build();

      picker.setVisible(true);
    };

    const syncDriveToS3 = async (selectedFiles: any[], prefix = '') => {
      const accessToken = localStorage.getItem('google_access_token');

      if (!accessToken) {
        console.error('❌ Không tìm thấy access token. Vui lòng đăng nhập lại.');
        hotToast.error('Không tìm thấy access token. Vui lòng đăng nhập lại.', {
          position: 'bottom-right',
        });
        return;
      }

      const toastId = hotToast.loading('Đang đồng bộ...', {
        position: 'bottom-right',
        autoClose: false,
      });

      try {
        const payload = {
          prefix: prefix, // ✅ Thêm prefix vào payload
          folders: [] as { id: string; name: string }[],
          files: [] as { id: string; name: string }[],
        };

        selectedFiles.forEach((file) => {
          if (file.mimeType === 'application/vnd.google-apps.folder') {
            payload.folders.push({ id: file.id, name: file.name });
          } else {
            payload.files.push({ id: file.id, name: file.name });
          }
        });

        const response = await axios.post(`${apiBaseUrl}/api/sync-drive-to-s3`, payload, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });

        ignoreCache.current = true;
        void refreshFiles();

        hotToast.update(toastId, {
          render: `Đồng bộ thành công! Đã tải lên ${response.data.uploadedKeys.length} file.`,
          type: 'success',
          isLoading: false,
          autoClose: 3000,
          position: 'bottom-right',
        });
      } catch (error) {
        console.error('❌ Lỗi khi đồng bộ file từ Google Drive:', error);

        hotToast.update(toastId, {
          render: 'Không thể đồng bộ file từ Google Drive.',
          type: 'error',
          isLoading: false,
          autoClose: 3000,
          position: 'bottom-right',
        });
      }
    };

    useEffect(() => {
      const handleLogoutMessage = (event: any) => {
        if (event.data.action === 'logout') {
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          iframe.src = 'https://accounts.google.com/Logout';
          document.body.appendChild(iframe);

          setTimeout(() => document.body.removeChild(iframe), 2000);
        }
      };

      window.addEventListener('message', handleLogoutMessage);
      return () => window.removeEventListener('message', handleLogoutMessage);
    }, []);

    useEffect(() => {
      const newSet = new Set(preSelectedFiles);
      setSelectedKeys(newSet);
    }, []);

    // Effect to show notifications for completed/failed jobs
    useEffect(() => {
      if (currentJob?.status) {
        if (currentJob.status.status === 'completed') {
          toast({
            title: 'Thu thập website hoàn tất',
            description: `Đã thu thập thành công ${currentJob.url} với ${currentJob.status.completed} trang`,
          });
          // Refresh files to show new content
          ignoreCache.current = true;
          void refreshFiles();
        } else if (currentJob.status.status === 'failed') {
          toast({
            title: 'Thu thập website thất bại',
            description: `Không thể thu thập ${currentJob.url}. Vui lòng thử lại.`,
            variant: 'destructive',
          });
        }
      }
    }, [currentJob?.status?.status]);

    const data = useMemo(() => fileSystem[currentPrefix] || [], [fileSystem, currentPrefix]);

    const filteredData = useMemo(() => data, [data, currentPrefix, fileSystem]);

    // Convert selectedKeys to row selection state for the table
    const rowSelection = useMemo(() => {
      const selection: Record<number, boolean> = {};
      filteredData.forEach((file, index) => {
        if (selectedKeys.has(file.key)) {
          selection[index] = true;
        }
      });
      return selection;
    }, [selectedKeys, filteredData]);

    // Check if a folder is partially selected
    const getFolderSelectionState = (folderKey: string): 'none' | 'partial' | 'all' => {
      const filesUnderFolder = getAllFilesUnderFolder(fileSystem, folderKey);
      // If the folder exists in the fileSystem but has no files, treat it as a single selectable item
      if (filesUnderFolder.length === 0) {
        return selectedKeys.has(folderKey) ? 'all' : 'none';
      }

      const selectedCount = filesUnderFolder.filter((key) => selectedKeys.has(key)).length;
      if (selectedCount === 0) return 'none';
      if (selectedCount === filesUnderFolder.length) return 'all';
      return 'partial';
    };

    const handleFolderClick = (prefix: string, isReplace = true) => {
      if (currentPrefix === prefix) return;

      if (idCreator && !currentPrefix) {
        setIsFolderClicking(true);
        setForcePrefix(prefix + idCreator);
        setCurrentPrefix(prefix + idCreator);
        // if (!Boolean(preSelectedFiles)) {
        //   setSelectedKeys(new Set());
        // }
      } else {
        setIsFolderClicking(true);
        setForcePrefix(prefix);
        setCurrentPrefix(prefix);
        // if (!Boolean(preSelectedFiles)) {
        //   setSelectedKeys(new Set());
        // }

        if (isReplace && !asSelector) {
          const basePath = window.location.pathname === '/' ? '' : window.location.pathname;

          const newUrl = prefix
            ? `${window.location.origin}${basePath}?prefix=${encodeURIComponent(prefix)}`
            : `${window.location.origin}${basePath}`;

          window.history.pushState({ path: newUrl }, '', newUrl);
        }
      }
    };

    useEffect(() => {
      if (!isFolderClicking) return;

      ignoreCache.current = true;
      refreshFiles().finally(() => setIsFolderClicking(false));
    }, [isFolderClicking]);

    const createFolder = async (name: string, prefix: string) => {
      setIsCreatingFolder(true);
      try {
        // Check if the folder name already exists in filtered data
        const folderExists = filteredData.some(
          (file) => file.isFolder && file.name.toLowerCase() === name.toLowerCase(),
        );

        if (folderExists) {
          toast({
            title: 'Lỗi',
            description: `Thư mục "${name}" đã tồn tại trong thư mục hiện tại.`,
            variant: 'destructive',
          });
          setIsCreatingFolder(false);
          return;
        }

        const folderPrefix = `${prefix}${name}/`;
        const response = await axios.post(`${apiBaseUrl}/api/create-folder`, {
          prefix: folderPrefix,
        });

        if (response.status !== 200) {
          toast({
            title: 'Lỗi',
            description: 'Không thể tạo thư mục',
            variant: 'destructive',
          });
          return;
        }

        ignoreCache.current = true;
        await refreshFiles();

        toast({
          title: 'Đã tạo thư mục',
          description: `Đã tạo thành công thư mục "${name}"`,
        });

        // Reset state
        setNewFolderName('');
        setIsCreateFolderOpen(false);
      } catch (error) {
        console.error('Error creating folder:', error);
        const errorMessage = (error as any)?.response?.data?.error || 'Không thể tạo thư mục';
        toast({
          title: 'Lỗi',
          description: errorMessage,
          variant: 'destructive',
        });
      } finally {
        setIsCreatingFolder(false);
      }
    };

    const uploadFiles = async (files: FileList, prefix: string, type: 'files' | 'folder') => {
      setUploadType(type);
      setIsUploading(true);
      try {
        const totalSize = Array.from(files).reduce((acc, file) => acc + file.size, 0);
        if (totalSize >= 100 * 1024 * 1024) {
          toast({
            title: 'Lỗi',
            description: 'Tổng kích thước các tập tin vượt quá 100 MB',
            variant: 'destructive',
          });
          setUploadType(null);
          setIsUploading(false);
          return;
        }

        // Check for empty files
        const emptyFiles = Array.from(files).filter((file) => file.size === 0);
        if (emptyFiles.length > 0) {
          const fileNames = emptyFiles.map((file) => file.name).join(', ');
          toast({
            title: 'Lỗi tải lên',
            description: `Không thể tải lên tập tin rỗng: ${fileNames}`,
            variant: 'destructive',
          });
          setIsUploading(false);
          setUploadType(null);
          return;
        }

        // Get user metadata for upload
        let dataLogin: any = localStorage.getItem('dataLogin');
        let user: any;

        try {
          if (dataLogin) {
            dataLogin = JSON.parse(dataLogin);
            user = dataLogin.user;
          }
        } catch (error) {
          dataLogin = null;
        }

        const formData = new FormData();

        if (user?.id) {
          formData.append(
            'metadata',
            JSON.stringify({
              creator: user.username,
              creatorId: user.id,
              DataSource: 'Tệp tải lên',
            }),
          );
        }
        if (type === 'folder' && files[0]) {
          const folderName = files[0].webkitRelativePath.split('/')[0];
          if (folderName) {
            formData.append('folderName', folderName);
          }
        }
        formData.append('prefix', prefix);
        Array.from(files).forEach((file) => {
          if (type === 'folder') {
            return formData.append('files', file, file.webkitRelativePath);
          } else {
            return formData.append('files', file);
          }
        });

        ignoreCache.current = true;

        const checkResponse = await axios.post(`${apiBaseUrl}/api/check-files-existed`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });

        if (checkResponse?.data?.status === 200) {
          await axios.post(`${apiBaseUrl}/api/upload`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
            timeout: 0, // no timeout (infinite)
          });
          setSearchQuery('');
          setForcePrefix(prefix);
          setTimeout(async () => {
            setUploadType(null);
            await refreshFiles();
            setIsUploading(false);
            await checkSyncingStatus();
          }, 500);
        } else {
          if (checkResponse?.data?.names?.length > 0 && !checkResponse?.data?.isFolder) {
            const filesFound: any = Array.from(files).filter((file) =>
              checkResponse.data.names.includes(file.name),
            );
            setDuplicateFiles(filesFound);
            setShowDuplicateDialog(true);
          }

          if (checkResponse?.data?.isFolder && checkResponse?.data?.name) {
            setFolderExist(checkResponse?.data?.name);
            setDuplicateFiles(files);
            setShowDuplicateDialog(true);
          }
        }
      } catch (error) {
        console.error('Lỗi tải lên:', error);
        const errorMessage = (error as any)?.response?.data?.error || 'Không thể tải lên tập tin';
        toast({
          title: 'Lỗi',
          description: errorMessage,
          variant: 'destructive',
        });
        setUploadType(null);
        setIsUploading(false);
      }
    };

    const overwriteFiles = async () => {
      try {
        setIsUploading(true);
        const formData = new FormData();

        if (duplicateFiles) {
          if (Boolean(folderExist)) {
            setUploadType('folder');
          } else {
            setUploadType('files');
          }
          setShowDuplicateDialog(false);
          let dataLogin: any = localStorage.getItem('dataLogin');
          let user: any;

          try {
            if (dataLogin) {
              dataLogin = JSON.parse(dataLogin);
              user = dataLogin.user;
            }
          } catch (error) {
            dataLogin = null;
          }

          if (user?.id) {
            console.log('🚀 ~ uploadFiles ~ user:', user);
            formData.append(
              'metadata',
              JSON.stringify({
                creator: user.username,
                creatorId: user.id,
                DataSource: 'Tệp tải lên',
              }),
            );
          }

          if (folderExist && duplicateFiles[0]) {
            const folderName = duplicateFiles[0].webkitRelativePath.split('/')[0];
            if (folderName) {
              formData.append('folderName', folderName);
            }
          }

          formData.append('prefix', currentPrefix);
          Array.from(duplicateFiles).forEach((file) => {
            if (folderExist && duplicateFiles[0]) {
              return formData.append('files', file, file.webkitRelativePath);
            } else {
              return formData.append('files', file);
            }
          });
          if (user?.id) {
            formData.append(
              'metadata',
              JSON.stringify({
                creator: user.username,
                creatorId: user.id,
                DataSource: 'Tệp tải lên',
              }),
            );
          }
          await axios.post(`${apiBaseUrl}/api/upload`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
            timeout: 0, // no timeout (infinite)
          });
          setSearchQuery('');
          setForcePrefix(currentPrefix);
          ignoreCache.current = true;
        }
      } catch (error) {
        console.error('Lỗi tải lên:', error);
        const errorMessage = (error as any)?.response?.data?.error || 'Không thể tải lên tập tin';
        toast({
          title: 'Lỗi',
          description: errorMessage,
          variant: 'destructive',
        });
      } finally {
        setTimeout(async () => {
          setUploadType(null);
          await refreshFiles();
          setIsUploading(false);
          setFolderExist('');
          setDuplicateFiles(null);
          await checkSyncingStatus();
        }, 500);
      }
    };

    const handleDelete = async (file: S3File) => {
      try {
        await axios.delete(`${apiBaseUrl}/api/files`, {
          data: {
            paths: [file.key],
          },
        });

        if (selectedKeys.has(file.key)) {
          const newSet = new Set(selectedKeys);
          newSet.delete(file.key);
          setSelectedKeys(newSet);
        }

        const updatedPrefix =
          file.key?.replace(/^(.*?)(\/.*)?$/, (_, firstPart, slashPart) => {
            if (!slashPart || slashPart === '/') return '';
            return firstPart + '/';
          }) || '';

        setForcePrefix(updatedPrefix);
        ignoreCache.current = true;

        // Show success toast
        toast({
          title: 'Đã xóa tập tin',
          description: `Đã xóa thành công "${file.name}"`,
        });

        setFileToDelete(null);
        await checkSyncingStatus();
      } catch (error) {
        console.error('Error deleting file:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể xóa tập tin',
          variant: 'destructive',
        });
      } finally {
        setIsDeleting(false);
        await refreshFiles();
      }
    };

    const handleRowSelectionChange = (
      updaterOrValue:
        | Record<number, boolean>
        | ((prev: Record<number, boolean>) => Record<number, boolean>),
    ) => {
      const newSelectedKeys = new Set(selectedKeys);

      const selection =
        typeof updaterOrValue === 'function' ? updaterOrValue(rowSelection) : updaterOrValue;

      // Get all indices in the current view
      const currentIndices = new Set(Object.keys(rowSelection).map(Number));

      // Handle deselection for indices that are no longer in the selection
      currentIndices.forEach((index) => {
        const file = filteredData[index];
        if (!file) return;

        if (!selection[index]) {
          newSelectedKeys.delete(file.key);
          if (file.isFolder) {
            getAllFilesUnderFolder(fileSystem, file.key).forEach((key) =>
              newSelectedKeys.delete(key),
            );
          }
        }
      });

      // Handle new selections
      Object.entries(selection).forEach(([indexStr, isSelected]) => {
        const index = parseInt(indexStr);
        const file = filteredData[index];
        if (!file) return;

        if (isSelected) {
          newSelectedKeys.add(file.key);
          if (file.isFolder) {
            getAllFilesUnderFolder(fileSystem, file.key).forEach((key) => newSelectedKeys.add(key));
          }
        }
      });

      setSelectedKeys(newSelectedKeys);
    };

    const handleRemoveSelected = async () => {
      setIsBulkDeleting(true);

      try {
        const selectedKeysArray = Array.from(selectedKeys);

        // Find selected files from the file system
        const selectedFiles = selectedKeysArray
          .map((key) =>
            Object.values(fileSystem)
              .flat()
              .find((f) => f.key === key),
          )
          .filter((file): file is S3File => file !== undefined);

        if (selectedFiles.length === 0) {
          toast({
            title: 'Lỗi',
            description: 'Không có tập tin nào được chọn để xóa',
            variant: 'destructive',
          });
          return;
        }

        await axios.delete(`${apiBaseUrl}/api/files`, {
          data: { paths: selectedKeysArray },
        });

        // Xử lý commonPrefix với regex mới
        const commonPrefix =
          selectedFiles[0]?.key.replace(/^(.*?)(\/.*)?$/, (_, firstPart, slashPart) => {
            if (!slashPart || slashPart === '/') return '';
            return firstPart + '/';
          }) || '';

        setForcePrefix(commonPrefix);
        // setCurrentPrefix(commonPrefix);
        ignoreCache.current = true;
        void refreshFiles();

        // Show success toast
        toast({
          title: 'Đã xóa tập tin',
          description: `Đã xóa thành công ${selectedFiles.length} mục`,
        });

        setShowBulkDeleteDialog(false);
        setSelectedKeys(new Set());
        await checkSyncingStatus();
      } catch (error) {
        console.error('Error deleting files:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể xóa các tập tin đã chọn',
          variant: 'destructive',
        });
      } finally {
        setIsBulkDeleting(false);
      }
    };

    const handleRename = async (file: S3File, newName: string) => {
      if (!currentPrefix.trim()) {
        toast({
          title: 'Lỗi',
          description: 'Không thể đổi tên tập tin hoặc thư mục ở thư mục gốc. Chỉ có thể xóa.',
          variant: 'destructive',
        });
        return;
      }
      setIsRenaming(true);
      try {
        const oldPath = file.key;
        let parentPath;
        if (file.isFolder) {
          // For folders, go up two levels to skip the folder name and trailing slash
          parentPath = oldPath.split('/').slice(0, -2).join('/') + '/';
        } else {
          // For files, just get the directory path
          parentPath = oldPath.substring(0, oldPath.lastIndexOf('/') + 1);
        }
        const newPath = parentPath + newName + (file.isFolder ? '/' : '');

        await axios.post(`${apiBaseUrl}/api/rename`, {
          oldPath,
          newPath,
        });

        // Refresh the file list
        ignoreCache.current = true;
        await refreshFiles();

        // Show success toast
        toast({
          title: 'Đã đổi tên',
          description: `Đã đổi tên thành công "${file.name}" thành "${newName}"`,
        });

        setFileToRename(null);
        setNewName('');
        setMetadataForEdit(null);
      } catch (error) {
        console.error('Error renaming item:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể đổi tên',
          variant: 'destructive',
        });
      } finally {
        setIsRenaming(false);
      }
    };

    const handleDownload = async (paths: string[]) => {
      // Add all paths to downloading set
      setDownloadingItems((prev) => new Set([...prev, ...paths]));
      try {
        const response = await axios.post(
          `${apiBaseUrl}/api/download`,
          { paths },
          { responseType: 'blob' },
        );

        // Create a URL for the blob
        const url = window.URL.createObjectURL(response.data);

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = url;

        // Get the filename from the Content-Disposition header
        const contentDisposition =
          response.headers['Content-Disposition'] || response.headers['content-disposition'];
        const filenameMatch = contentDisposition?.match(/filename="(.+)"/);

        // For single files, use the filename from the header
        // For multiple files or folders, fallback to download.zip
        const filename = filenameMatch ? decodeURIComponent(filenameMatch[1]) : 'download.zip';

        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast({
          title: 'Bắt đầu tải xuống',
          description: `Đã bắt đầu tải xuống ${paths.length} mục${paths.length > 1 ? '' : ''}`,
        });
      } catch (error) {
        console.error('Error downloading files:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể tải xuống tập tin',
          variant: 'destructive',
        });
      } finally {
        // Remove all paths from downloading set
        setDownloadingItems((prev) => {
          const next = new Set(prev);
          paths.forEach((path) => next.delete(path));
          return next;
        });
      }
    };

    const prefixSegments = currentPrefix
      ? currentPrefix
          .replace(rootPrefix, '') // Remove ROOT_PREFIX from the display
          .split('/')
          .filter(Boolean)
      : [];

    // Get selected files
    const getSelectedFiles = () => {
      let currentSelections = Array.from(selectedKeys).map((key) => ({ key })) as S3File[];

      if (currentSelections.length > 1) {
        currentSelections = currentSelections.filter((v) => v.key !== rootPrefix);
      }

      const haveFile = (folderKey: string) => {
        return (
          findIndex(
            currentSelections,
            (file) => !file.key.endsWith('/') && file.key.startsWith(folderKey),
          ) !== -1
        );
      };

      currentSelections = currentSelections.filter((file) => {
        if (file.key.endsWith('/')) return !haveFile(file.key);
        return true;
      });

      // console.log('currentSelected', currentSelections, rootPrefix);

      return currentSelections;
    };

    // Define columns based on selector mode
    const columns = useMemo<ColumnDef<S3File>[]>(() => {
      const baseColumns = [
        {
          id: 'select',
          header: () => {
            const filesInView = getAllFilesInCurrentView(fileSystem, currentPrefix);
            const allSelected =
              filesInView.length > 0 && filesInView.every((key) => selectedKeys.has(key));
            const someSelected = filesInView.some((key) => selectedKeys.has(key));

            return (
              <Checkbox
                checked={allSelected ? true : someSelected ? 'indeterminate' : false}
                onCheckedChange={(checked) => {
                  if (checked) {
                    const allSelection = filteredData.reduce(
                      (acc, _, index) => {
                        acc[index] = true;
                        return acc;
                      },
                      {} as Record<number, boolean>,
                    );

                    handleRowSelectionChange(allSelection);
                  } else {
                    for (const selectedKey of Array.from(selectedKeys)) {
                      if (selectedKey.startsWith(currentPrefix)) {
                        selectedKeys.delete(selectedKey);
                        selectStates.current[selectedKey] = false;
                      }
                    }

                    forEach(selectStates.current, (_value, key) => {
                      if (key.startsWith(currentPrefix)) {
                        selectStates.current[key] = false;
                      }
                    });

                    handleRowSelectionChange({});
                  }
                }}
                aria-label="Chọn tất cả"
              />
            );
          },
          cell: ({ row }: any) => {
            const file = row.original;
            const isFolder = file.isFolder;
            let selectionState = isFolder
              ? getFolderSelectionState(file.key)
              : selectedKeys.has(file.key)
                ? 'all'
                : 'none';

            if (selectionState === 'none' && file.key.endsWith('/')) {
              for (const selectedKey of Array.from(selectedKeys)) {
                if (selectedKey.startsWith(file.key)) {
                  selectionState = 'all';
                  break;
                }
              }
            }

            return (
              <div className="flex items-center">
                <Checkbox
                  checked={
                    selectionState === 'partial' ? 'indeterminate' : selectionState === 'all'
                  }
                  onCheckedChange={(checked) => {
                    const newSelection = { ...rowSelection };
                    if (checked) {
                      newSelection[row.index] = true;
                    } else {
                      delete newSelection[row.index];
                    }

                    selectStates.current[row.original?.key || '_'] = Boolean(checked);

                    if (row.original?.key.endsWith('/')) {
                      for (const selectedKey of Array.from(selectedKeys)) {
                        if (selectedKey.startsWith(row.original.key)) {
                          selectStates.current[row.original.key] = false;
                          selectedKeys.delete(selectedKey);
                        }
                      }
                    }

                    handleRowSelectionChange(newSelection);
                  }}
                  aria-label="Chọn dòng"
                />
              </div>
            );
          },
          enableSorting: false,
        },
        {
          id: 'fileName',
          header: ({ column }: any) => {
            return (
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
              >
                Tên tập tin
                <ArrowUpDown />
              </Button>
            );
          },
          accessorKey: 'name',
          cell: ({ row }: any) => {
            const file = row.original;
            const parentPrefix = file.key.substring(0, file.key.lastIndexOf('/')) || '/';
            const showPrefix = searchQuery.trim() !== '' && parentPrefix !== currentPrefix;

            // Split the parent prefix into segments for individual folder links
            const prefixSegments = parentPrefix.split('/').filter(Boolean);

            return (
              <div className="flex flex-col group">
                <div className="flex items-center">
                  <div
                    className={`flex items-center gap-2 ${file.isFolder ? 'cursor-pointer hover:text-blue-600' : ''}`}
                    onClick={() => {
                      if (file.isFolder) {
                        // if (!preSelectedFiles) {
                        //   setSelectedKeys(new Set());
                        // }
                        handleFolderClick(file.key);
                      }
                    }}
                  >
                    {file.isFolder ? <FolderIcon className="h-5 w-5 text-blue-500" /> : null}
                    {file.name}
                  </div>
                  <button
                    className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigator.clipboard.writeText(file.key);
                      toast({
                        title: 'Copied to clipboard',
                        description: `Path: ${file.key}`,
                        duration: 2000,
                      });
                    }}
                    title="Copy path to clipboard"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </button>
                </div>
                {showPrefix && (
                  <div className="text-sm text-gray-500 flex items-center">
                    <FolderIcon className="h-4 w-4 text-gray-500 my-auto" />
                    <span className="flex items-center ml-1">
                      <span
                        className="cursor-pointer hover:text-blue-600"
                        onClick={() => {
                          // searchBoxRef.current?.setValue('');
                          handleFolderClick('');
                        }}
                      >
                        /
                      </span>
                      {prefixSegments.map((segment: string, index: number) => (
                        <span key={index} className="flex items-center">
                          <span
                            className="cursor-pointer hover:text-blue-600"
                            onClick={() => {
                              // searchBoxRef.current?.setValue('');
                              handleFolderClick(prefixSegments.slice(0, index + 1).join('/') + '/');
                            }}
                          >
                            {segment}
                          </span>
                          {index < prefixSegments.length - 1 && <span>/</span>}
                        </span>
                      ))}
                    </span>
                  </div>
                )}
              </div>
            );
          },
        },
      ];

      if (!asSelector) {
        baseColumns.push(
          {
            id: 'size',
            header: ({ column }: any) => {
              return (
                <Button
                  variant="ghost"
                  onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                >
                  Kích thước
                  <ArrowUpDown />
                </Button>
              );
            },
            accessorKey: 'size',
            cell: ({ getValue, row }) => {
              const value = getValue();
              return <span>{row.original.isFolder ? '-' : formatBytes(value as number)}</span>;
            },
          },
          {
            id: 'lastModified',
            header: ({ column }: any) => {
              return (
                <Button
                  variant="ghost"
                  onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                  className={'px-0 w-full flex-row justify-end'}
                >
                  Sửa đổi lần cuối
                  <ArrowUpDown />
                </Button>
              );
            },
            accessorKey: 'lastModified',
            cell: ({ row }: any) => {
              const value = row.original.lastModified as Date;
              return (
                <div className={'w-full flex-row justify-end items-center text-right'}>
                  <span>
                    {value && dayjs(value).isValid()
                      ? dayjs(value).format('DD/MM/YYYY HH:mm')
                      : '-'}
                  </span>
                </div>
              );
            },
            size: 200,
          } as any,
          {
            id: 'actions',
            header: () => {
              return <span>Hành động</span>;
            },
            accessorKey: 'key',
            cell: ({ row }) => (
              <div className="flex items-center gap-2">
                {currentPrefix === '' && rootUrlHostApi && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setFolderToShowFlow(row.original.key);
                            setShowInfoFlowsDialogs(true);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          {downloadingItems.has(row.original.key) ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <CircleStackIcon className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Những Flow sử dụng Folder này làm Data</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDownload([row.original.key])}
                        className="h-8 w-8 p-0"
                        disabled={downloadingItems.has(row.original.key)}
                      >
                        {downloadingItems.has(row.original.key) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <ArrowDownTrayIcon className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Tải xuống</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {currentPrefix.trim() !== '' && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setFileToRename(row.original);
                            setNewName(row.original.name);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Đổi tên</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setOpenDialogRename(true);
                          setFileToMove(row.original);
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <MoveUpRight className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Di chuyển</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setFileToDelete(row.original)}
                        className="h-8 w-8 p-0"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Xóa</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setFileToShare(row.original)}
                        className="h-8 w-8 p-0"
                      >
                        <ShareIcon className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Chia sẻ</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            ),
          },
        );
      }

      return baseColumns;
    }, [selectedKeys, filteredData, rowSelection, downloadingItems, asSelector]);

    // Create table instance
    const table = useReactTable({
      data: filteredData,
      columns,
      state: {
        rowSelection,
        sorting,
      },
      onSortingChange: setSorting,
      enableRowSelection: true,
      onRowSelectionChange: handleRowSelectionChange,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
    });

    // Add loadFileContent function
    const loadFileContent = async (file: S3File) => {
      setIsLoadingContent(true);
      try {
        if (isTextFile(file)) {
          const responseContentFile = await axios.get(`${apiBaseUrl}/api/read-text`, {
            params: { path: file.key },
          });
          setFileContent(responseContentFile.data.content);
        }

        try {
          const responseMetadata = await axios.get(`${apiBaseUrl}/api/read-json`, {
            params: { path: file.key + '.metadata.json' },
          });
          const metadataAttributes = responseMetadata?.data?.content?.metadataAttributes || null;
          return setMetadataForEdit({ fullPath: fileToRename?.key, ...metadataAttributes });
        } catch (error) {
          return setMetadataForEdit('Lỗi phân tích siêu dữ liệu');
        }
      } catch (error) {
        console.error('Error loading file content:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể tải nội dung tập tin',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingContent(false);
      }
    };

    // Add saveFileContent function
    const saveFileContent = async (file: S3File, content: string) => {
      setIsSavingContent(true);
      try {
        await axios.post(`${apiBaseUrl}/api/modify-text`, {
          path: file.key,
          content,
        });

        // Show success toast
        toast({
          title: 'Đã lưu tập tin',
          description: 'Nội dung tập tin đã được cập nhật thành công',
        });

        // Refresh the file list to update metadata
        ignoreCache.current = true;
        await refreshFiles();

        // Close the dialog after successful save
        setFileToRename(null);
        setFileContent('');
        setActiveTab('rename');
        await checkSyncingStatus();
      } catch (error) {
        console.error('Error saving file content:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể lưu nội dung tập tin',
          variant: 'destructive',
        });
      } finally {
        setIsSavingContent(false);
      }
    };

    // Add saveFileContent function
    const saveJsonFile = async (file: S3File) => {
      setIsSavingContent(true);
      try {
        // console.log('🚀 ~ saveJsonFile ~ metadataForEdit:', metadataForEdit);

        await axios.post(`${apiBaseUrl}/api/modify-json`, {
          path: file.key + '.metadata.json',
          content: { metadataAttributes: metadataForEdit },
        });

        // Show success toast
        toast({
          title: 'Đã lưu tập tin',
          description: 'Nội dung tập tin đã được cập nhật thành công',
        });

        // Refresh the file list to update metadata
        ignoreCache.current = true;
        await refreshFiles();

        // Close the dialog after successful save
        setFileToRename(null);
        setFileContent('');
        setActiveTab('rename');
      } catch (error) {
        console.error('Error saving file content:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể lưu nội dung tập tin',
          variant: 'destructive',
        });
      } finally {
        setIsSavingContent(false);
      }
    };

    // Modify the useEffect to load content when fileToRename changes
    useEffect(() => {
      if (fileToRename) {
        if (!fileToRename.isFolder) {
          void loadFileContent(fileToRename);
        }
        setActiveTab('rename'); // Reset to rename tab when opening dialog
      }
    }, [fileToRename]);

    const crawlWebsite = async (url: string, limit: number) => {
      // If there's already a job running, don't start another one
      if (currentJob) {
        toast({
          title: 'Đang thu thập website',
          description: 'Vui lòng đợi cho công việc thu thập hiện tại hoàn thành.',
          variant: 'destructive',
        });
        return;
      }

      setIsCrawling(true);
      try {
        const { data: jobInfo } = await axios.post(`${apiBaseUrl}/api/crawl`, {
          url,
          limit,
          prefix: currentPrefix,
        });

        if (jobInfo.id) {
          // Set current job
          setCurrentJob({
            id: jobInfo.id,
            url,
            limit,
            prefix: currentPrefix,
            taskId: jobInfo.task?.id || '',
          });

          // Store in history
          const storedJobs = localStorage.getItem(CRAWL_JOBS_KEY);
          const crawlJobs = storedJobs
            ? (JSON.parse(storedJobs) as {
                url: string;
                id: string;
                limit: number;
                prefix: string;
                timestamp: number;
              }[])
            : [];

          if (!crawlJobs.some((job) => job.id === jobInfo.id)) {
            crawlJobs.unshift({
              url,
              id: jobInfo.id,
              limit,
              prefix: currentPrefix,
              timestamp: Date.now(),
            });
            if (crawlJobs.length > 20) {
              crawlJobs.length = 20;
            }
            localStorage.setItem(CRAWL_JOBS_KEY, JSON.stringify(crawlJobs));
          }

          toast({
            title: 'Bắt đầu thu thập website',
            description: `Đã thêm nhiệm vụ thu thập ${url} với giới hạn ${limit}. Dữ liệu sẽ có sẵn sau khi thu thập hoàn tất.`,
          });

          setIsWebsiteDialogOpen(false);
          setWebsiteUrl('');
          setCrawlLimit('1');
        }
      } catch (error) {
        console.error('Error crawling website:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể bắt đầu thu thập website',
          variant: 'destructive',
        });
      } finally {
        setIsCrawling(false);
      }
    };

    const stopCurrentJob = async () => {
      try {
        if (!currentJob?.id) return;

        // await axios.post(`${apiBaseUrl}/api/crawl/${currentJob.id}/stop`);

        // Clear current job from state and localStorage
        setCurrentJob(null);
        localStorage.removeItem(CURRENT_CRAWL_JOB_KEY);

        toast({
          title: 'Đã dừng thu thập',
          description: `Đã dừng thu thập website ${currentJob.url}`,
        });
      } catch (error) {
        console.error('Error stopping job:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể dừng thu thập website',
          variant: 'destructive',
        });
      }
    };

    useEffect(() => {
      if (asSelector) return;

      const handlePopState = (changeSelectKey = true) => {
        const url = new URL(window.location.href);
        const prefixParam = url.searchParams.get('prefix') || '';

        if (prefixParam) {
          handleFolderClick(prefixParam, false);
        } else {
          if (changeSelectKey && !Boolean(preSelectedFiles)) {
            setSelectedKeys(new Set());
          }
          setForcePrefix(rootPrefix);
          setCurrentPrefix(rootPrefix);
          ignoreCache.current = true;

          setTimeout(() => {
            void refreshFiles();
          }, 0);
        }
      };

      // Handle initial URL on mount
      handlePopState(false);

      window.addEventListener('popstate', () => handlePopState(true));
      return () => {
        window.removeEventListener('popstate', () => handlePopState(true));
      };
    }, []);

    const handleExportReport = async () => {
      setIsLoadingHandleExportReport(true);
      try {
        await axios.post(`${apiBaseUrl}/api/prediction-report`);

        // console.log('🚀 ~ Home.tsx:1585 ~ handleExportReport ~ response:', response);

        toast({
          title: 'Xuất báo cáo thành công',
          description: 'Báo cáo dự đoán đã được xuất',
        });
      } catch (error) {
        console.error('Error exporting report:', error);
        toast({
          title: 'Lỗi',
          description: 'Không thể xuất báo cáo dự đoán',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingHandleExportReport(false);
      }
    };

    const handleSyncKnowledgeBase = async () => {
      try {
        const user = getUserInfoFromLocal();

        if (user) {
          const response = await axios.post(`${apiBaseUrl}/api/start-ingestion-job`, {
            prefix: currentPrefix,
            user_id: user.id,
            userName: user.username,
          });
          if (response.status === 200) {
            await checkSyncingStatus();
          }
        }
      } catch (error) {
        setIsSyncing(false);
        console.log(error);
      }
    };

    const interval = useRef<any>();

    const checkSyncingStatus = async () => {
      try {
        setIsSyncing(true);
        const response = await axios.post(`${apiBaseUrl}/api/get-ingestion-job-status`, {
          prefix: currentPrefix,
        });

        // status: "STARTING" || "IN_PROGRESS" || "COMPLETE" || "FAILED" || "STOPPING" || "STOPPED"
        const { status } = response?.data;
        if (
          status === 'FAILED' ||
          status === 'COMPLETE' ||
          status === 'STOPPED' ||
          status === 'STOPPING' ||
          !status
        ) {
          setIsSyncing(false);
          if (interval.current) {
            clearInterval(interval.current);
            interval.current = null;
          }
          setSyncJobStatus(response?.data);
        } else {
          if (!interval.current) {
            interval.current = setInterval(() => checkSyncingStatus(), 5000);
          }
          setSyncJobStatus(response?.data);
        }
      } catch (error) {
        console.log(error);
      }
    };

    const calculateSelectedFiles = () => {
      const keys = Array.from(selectedKeys);
      let count: number;

      if (!currentPrefix) {
        count = new Set(keys.filter((key) => key.split('/').shift())).size;
      } else {
        count = keys.filter((key) => key.startsWith(currentPrefix) && key !== currentPrefix).length;
      }

      return `Đang chọn: ${count}`;
    };

    // console.log(syncJobStatus);

    // Check sync status every 5s
    useEffect(() => {
      const newRootFolderName = currentPrefix?.split('/')[0];

      if (newRootFolderName !== rootFolderName) {
        setIsSyncing(false);
        if (interval.current) {
          clearInterval(interval.current);
          interval.current = null;
        }
        setRootFolderName(newRootFolderName || '');
        setSyncJobStatus(null);

        if (newRootFolderName) {
          checkSyncingStatus();
        }
      }

      return () => {};
    }, [rootFolderName, currentPrefix]);

    useEffect(() => {
      if (isSharePointModalOpen && sharePointSites.length === 0) {
        fetchSharePointSites();
        setSelectedSite(null);
      }
    }, [isSharePointModalOpen]);

    useEffect(() => {
      if (preSelectedFiles) {
        for (const key of Array.from(preSelectedFiles)) {
          selectStates.current[key] = true;
        }
      }
    }, []);

    return (
      <div className="space-y-4 s3-explr-container">
        <Toaster />

        <Dialog
          open={showDuplicateDialog}
          onOpenChange={(e) => {
            setShowDuplicateDialog(e);
            if (!e) {
              setFolderExist('');
              setDuplicateFiles(null);
              setIsUploading(false);
              setUploadType(null);
            }
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cảnh báo trùng lặp</DialogTitle>
              <DialogDescription>
                {folderExist && <>Folder ${folderExist} đã có.</>}
                {duplicateFiles && duplicateFiles.length > 0 && (
                  <>
                    Những tệp sau bị lặp:
                    <ul className="mt-2 list-disc pl-5 text-sm text-red-600">
                      {Array.from(duplicateFiles).map((file: File, index: number) => (
                        <li key={index}>{file.name}</li>
                      ))}
                    </ul>
                  </>
                )}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowDuplicateDialog(false);
                  setFolderExist('');
                  setDuplicateFiles(null);
                  setIsUploading(false);
                  setUploadType(null);
                }}
              >
                Bỏ qua
              </Button>
              <Button variant="destructive" onClick={() => overwriteFiles()}>
                Ghi đè
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Breadcrumb className="s3-explr-breadcrumb">
          <BreadcrumbList className="s3-explr-breadcrumb-list">
            <BreadcrumbItem className="s3-explr-breadcrumb-item">
              <BreadcrumbLink
                onClick={() => handleFolderClick(rootPrefix)}
                className="flex items-center gap-1 cursor-pointer s3-explr-breadcrumb-home"
              >
                {homeLabel}
              </BreadcrumbLink>
            </BreadcrumbItem>
            {!idCreator &&
              prefixSegments.map((segment, index) => (
                <div key={index} className="flex items-center">
                  <BreadcrumbSeparator
                    key={`sep-${index}`}
                    className="s3-explr-breadcrumb-separator"
                  />
                  <BreadcrumbItem key={`item-${segment}`} className="s3-explr-breadcrumb-item">
                    <BreadcrumbLink
                      onClick={() =>
                        handleFolderClick(
                          rootPrefix + prefixSegments.slice(0, index + 1).join('/') + '/',
                        )
                      }
                      className="cursor-pointer s3-explr-breadcrumb-link"
                    >
                      {segment}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                </div>
              ))}
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex items-center space-x-4 s3-explr-toolbar">
          <div className="relative max-w-sm w-full s3-explr-search-container">
            <SearchBox
              ref={searchBoxRef}
              onChange={(searchQuery1) => setSearchQuery(searchQuery1)}
            />
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setForcePrefix(currentPrefix);
              ignoreCache.current = true;
              void refreshFiles();
            }}
            disabled={isLoading}
            className="flex items-center gap-2 s3-explr-refresh-btn"
          >
            <ArrowPathIcon
              className={`h-4 w-4 s3-explr-refresh-icon ${isLoading ? 'animate-spin' : ''}`}
            />
            Làm mới
          </Button>

          {currentPrefix && currentPrefix !== '/' && (
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (isLoading || isSyncing) return;
                      setIsSyncing(true);
                      handleSyncKnowledgeBase();
                    }}
                    aria-disabled={isLoading || isSyncing}
                    className="flex items-center gap-2 s3-explr-refresh-btn aria-disabled:opacity-50"
                  >
                    <ArrowPathIcon
                      className={`h-4 w-4 s3-explr-refresh-icon ${isLoading || isSyncing ? 'animate-spin' : ''}`}
                    />
                    Đồng bộ KB
                  </Button>
                </TooltipTrigger>
                {syncJobStatus && (
                  <TooltipContent side="right" sideOffset={5}>
                    <div className="space-y-2 p-3 max-w-md">
                      <p className="font-semibold text-lg border-b pb-1">Thông tin đồng bộ</p>

                      {/* Status Section */}
                      <div className="space-y-1">
                        <p className="flex items-center gap-2">
                          <span className="font-medium">Trạng thái:</span>
                          <span
                            className={`flex items-center gap-1 font-semibold ${
                              syncJobStatus.status === 'COMPLETE'
                                ? 'text-green-600'
                                : syncJobStatus.status === 'FAILED'
                                  ? 'text-red-600'
                                  : 'text-yellow-600'
                            }`}
                          >
                            {syncJobStatus.status === 'COMPLETE' && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                strokeWidth={2}
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            )}
                            {syncJobStatus.status === 'FAILED' && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                strokeWidth={2}
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                            )}
                            {syncJobStatus.status !== 'COMPLETE' &&
                              syncJobStatus.status !== 'FAILED' && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4 animate-spin"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                  strokeWidth={2}
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                                  />
                                </svg>
                              )}
                            {syncJobStatus.status}
                          </span>
                        </p>
                      </div>

                      {/* Statistics Section */}
                      {syncJobStatus.statistics && (
                        <div className="space-y-1 border-t pt-2">
                          <p className="font-medium text-gray-50">Thống kê xử lý:</p>
                          <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Tài liệu quét:</span>
                              <span className="font-medium">
                                {syncJobStatus.statistics.numberOfDocumentsScanned}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Metadata quét:</span>
                              <span className="font-medium">
                                {syncJobStatus.statistics.numberOfMetadataDocumentsScanned}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Tài liệu mới:</span>
                              <span className="font-medium text-green-600">
                                {syncJobStatus.statistics.numberOfNewDocumentsIndexed}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Tài liệu sửa:</span>
                              <span className="font-medium text-blue-600">
                                {syncJobStatus.statistics.numberOfModifiedDocumentsIndexed}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Metadata sửa:</span>
                              <span className="font-medium text-blue-600">
                                {syncJobStatus.statistics.numberOfMetadataDocumentsModified}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Tài liệu xóa:</span>
                              <span className="font-medium text-orange-600">
                                {syncJobStatus.statistics.numberOfDocumentsDeleted}
                              </span>
                            </div>
                            <div className="flex justify-between col-span-2">
                              <span>Tài liệu lỗi:</span>
                              <span className="font-medium text-red-600">
                                {syncJobStatus.statistics.numberOfDocumentsFailed}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Job Details Section */}
                      <div className="space-y-1 border-t pt-2 text-sm text-gray-200">
                        <p className="font-medium text-gray-50">Chi tiết Job:</p>
                        <p>
                          <span className="font-medium">DataSource ID:</span>{' '}
                          {syncJobStatus.dataSourceId}
                        </p>
                        <p>
                          <span className="font-medium">Ingestion Job ID:</span>{' '}
                          {syncJobStatus.ingestionJobId}
                        </p>
                        <p>
                          <span className="font-medium">Knowledge Base ID:</span>{' '}
                          {syncJobStatus.knowledgeBaseId}
                        </p>
                        <p>
                          <span className="font-medium">Job trong queue:</span>{' '}
                          {syncJobStatus.is_still_in_stock ? 'Còn' : 'Không'}
                        </p>
                      </div>

                      {/* Error Message */}
                      {syncJobStatus.errorMessage && (
                        <div className="border-t pt-2">
                          <p className="text-red-600 font-medium">Lỗi:</p>
                          <p className="text-red-600 text-sm bg-red-50 p-2 rounded">
                            {syncJobStatus.errorMessage}
                          </p>
                        </div>
                      )}
                    </div>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          )}
          {/* Nút Add file mới */}
          <div className="relative" ref={addFileMenuRef}>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2 s3-explr-add-file-btn bg-[#1682fe] hover:bg-[#0f6cd4] text-white rounded-md px-4 py-2 transition-colors"
              onClick={() => setShowAddFileMenu((prev) => !prev)}
            >
              <PlusIcon className="w-4 h-4" />
              Add file
            </Button>
            {showAddFileMenu && (
              <div className="absolute z-50 mt-2 right-0 w-56 bg-white border rounded-lg shadow-lg py-2 flex flex-col">
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  onClick={() => {
                    setShowAddFileMenu(false);
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.multiple = true;
                    input.onchange = (e) => {
                      const files = (e.target as HTMLInputElement).files;
                      if (files) {
                        void uploadFiles(files, currentPrefix, 'files');
                      }
                    };
                    input.click();
                  }}
                >
                  <ArrowUpTrayIcon className="h-4 w-4 mr-2" /> Tải tệp tin
                </button>
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  onClick={() => {
                    setShowAddFileMenu(false);
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.multiple = true;
                    input.webkitdirectory = true;
                    input.onchange = (e) => {
                      const files = (e.target as HTMLInputElement).files;
                      if (files) {
                        void uploadFiles(files, currentPrefix, 'folder');
                      }
                    };
                    input.click();
                  }}
                >
                  <FolderIcon className="h-4 w-4 mr-2" /> Tải lên thư mục
                </button>
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  disabled={Boolean(displayPrefixes) && !currentPrefix}
                  onClick={() => setIsCreateFolderOpen(true)}
                >
                  <FolderPlusIcon className="h-4 w-4 mr-2 s3-explr-folder-plus-icon" />
                  Thư mục mới
                </button>
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  onClick={() => {
                    setShowAddFileMenu(false);
                    setIsWebsiteDialogOpen(true);
                  }}
                >
                  <GlobeAltIcon className="h-4 w-4 mr-2" /> Thêm Website
                </button>
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  onClick={() => {
                    setShowAddFileMenu(false);
                    login();
                  }}
                >
                  <svg
                    viewBox="0 0 87.3 78"
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2"
                  >
                    <path
                      d="m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z"
                      fill="#0066da"
                    />
                    <path
                      d="m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z"
                      fill="#00ac47"
                    />
                    <path
                      d="m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z"
                      fill="#ea4335"
                    />
                    <path
                      d="m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z"
                      fill="#00832d"
                    />
                    <path
                      d="m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z"
                      fill="#2684fc"
                    />
                    <path
                      d="m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z"
                      fill="#ffba00"
                    />
                  </svg>{' '}
                  Đồng bộ Google Drive
                </button>
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  onClick={() => {
                    setShowAddFileMenu(false);
                    loginOneDrive();
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 64 64"
                    className="h-4 w-4 mr-2"
                  >
                    <path
                      fill="#094ab2"
                      d="M47.3 28.2C45.3 18.6 37.1 12 28 12c-8.5 0-15.9 5.6-18.4 13.5C4.2 27.2 0 32.2 0 38c0 6.6 5.4 12 12 12h36c8.3 0 16-6.6 16-15.5 0-6.8-4.9-12.4-11.7-13.3z"
                    />
                    <path
                      fill="#0067c5"
                      d="M44.9 30.1c-.2 0-.4-.1-.6-.1H26c-3.3 0-6 2.7-6 6 0 .9.2 1.8.6 2.6-1.7-.7-3.5-1.1-5.5-1.1C7.6 37.5 4 41.5 4 46s3.6 8.5 8.1 8.5h34.3c6.8 0 12.6-5.4 12.6-12s-5.5-11.6-11.1-12.4z"
                    />
                  </svg>{' '}
                  Đồng bộ OneDrive
                </button>
                <button
                  className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 text-left"
                  onClick={() => {
                    setShowAddFileMenu(false);
                    loginSharePoint();
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="h-4 w-4 mr-2"
                  >
                    <path
                      fill="#036C70"
                      d="M19 14.5v-9c0-.28-.22-.5-.5-.5h-13c-.28 0-.5.22-.5.5v9c0 .28.22.5.5.5h13c.28 0 .5-.22.5-.5z"
                    />
                    <path
                      fill="#17828F"
                      d="M16 17.5v-9c0-.28-.22-.5-.5-.5h-13c-.28 0-.5.22-.5.5v9c0 .28.22.5.5.5h13c.28 0 .5-.22.5-.5z"
                    />
                    <path
                      fill="#4DB6AC"
                      d="M13 20.5v-9c0-.28-.22-.5-.5-.5h-13c-.28 0-.5.22-.5.5v9c0 .28.22.5.5.5h13c.28 0 .5-.22.5-.5z"
                    />
                    <path
                      fill="#fff"
                      d="M4.5 15.5v-3c0-.28.22-.5.5-.5h3c.28 0 .5.22.5.5v3c0 .28-.22.5-.5.5H5c-.28 0-.5-.22-.5-.5z"
                    />
                  </svg>{' '}
                  Đồng bộ SharePoint
                </button>
              </div>
            )}
          </div>
          {!asSelector && (
            <>
              {selectedKeys.size > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(Array.from(selectedKeys))}
                    disabled={Array.from(selectedKeys).some((key) => downloadingItems.has(key))}
                    className="flex items-center gap-2 s3-explr-download-selected-btn"
                  >
                    {Array.from(selectedKeys).some((key) => downloadingItems.has(key)) ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2 s3-explr-loader" />
                    ) : (
                      <ArrowDownTrayIcon className="h-4 w-4 s3-explr-download-icon" />
                    )}
                    Tải xuống đã chọn ({selectedKeys.size})
                  </Button>

                  <Dialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
                    <DialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="flex items-center gap-2 s3-explr-bulk-delete-btn"
                      >
                        <TrashIcon className="h-4 w-4 s3-explr-trash-icon" />
                        Xóa đã chọn ({selectedKeys.size})
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="s3-explr-dialog-content">
                      <DialogHeader className="s3-explr-dialog-header">
                        <DialogTitle className="s3-explr-dialog-title">
                          Xác nhận xóa hàng loạt
                        </DialogTitle>
                        <DialogDescription className="s3-explr-dialog-description">
                          Bạn có chắc chắn muốn xóa {selectedKeys.size} mục đã chọn? Hành động này
                          không thể hoàn tác.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter className="s3-explr-dialog-footer">
                        <Button
                          variant="outline"
                          onClick={() => setShowBulkDeleteDialog(false)}
                          className="s3-explr-cancel-btn"
                        >
                          Hủy
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleRemoveSelected}
                          disabled={isBulkDeleting}
                          className="s3-explr-confirm-delete-btn"
                        >
                          {isBulkDeleting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                              Đang xóa...
                            </>
                          ) : (
                            'Xóa đã chọn'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </>
              )}
              {!idCreator && (
                <Dialog open={isCreateFolderOpen} onOpenChange={setIsCreateFolderOpen}>
                  <DialogTrigger asChild></DialogTrigger>
                  <DialogContent className="s3-explr-dialog-content">
                    <DialogHeader className="s3-explr-dialog-header">
                      <DialogTitle className="s3-explr-dialog-title">Tạo thư mục mới</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4 s3-explr-dialog-body">
                      <div className="space-y-2 s3-explr-input-group">
                        <Label htmlFor="name" className="s3-explr-label">
                          Tên thư mục
                        </Label>
                        <Input
                          disabled={isCreatingFolder}
                          id="name"
                          value={newFolderName}
                          onChange={(e) => setNewFolderName(e.target.value)}
                          placeholder="Nhập tên thư mục"
                          className="s3-explr-input"
                        />
                      </div>
                      <Button
                        onClick={() => {
                          if (newFolderName.trim()) {
                            void createFolder(newFolderName, currentPrefix);
                          }
                        }}
                        disabled={isCreatingFolder}
                        className="s3-explr-create-btn"
                      >
                        {isCreatingFolder && (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                        )}
                        {isCreatingFolder ? 'Đang tạo...' : 'Tạo'}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
              {idCreator && !currentPrefix ? (
                <></>
              ) : (
                <div className="flex items-center gap-2 s3-explr-upload-group">
                  {/*<Button*/}
                  {/*  variant="outline"*/}
                  {/*  size="sm"*/}
                  {/*  className="flex items-center gap-2 s3-explr-upload-files-btn"*/}
                  {/*  onClick={() => {*/}
                  {/*    const input = document.createElement('input');*/}
                  {/*    input.type = 'file';*/}
                  {/*    input.multiple = true;*/}
                  {/*    input.onchange = (e) => {*/}
                  {/*      const files = (e.target as HTMLInputElement).files;*/}
                  {/*      if (files) {*/}
                  {/*        void uploadFiles(files, currentPrefix, 'files');*/}
                  {/*      }*/}
                  {/*    };*/}
                  {/*    input.click();*/}
                  {/*  }}*/}
                  {/*  disabled={uploadType !== null || (Boolean(displayPrefixes) && !currentPrefix)}*/}
                  {/*>*/}
                  {/*  {uploadType === 'files' ? (*/}
                  {/*    <>*/}
                  {/*      <Loader2 className="h-4 w-4 animate-spin mr-2 s3-explr-loader" />*/}
                  {/*      Đang xử lý...*/}
                  {/*    </>*/}
                  {/*  ) : (*/}
                  {/*    <>*/}
                  {/*      <ArrowUpTrayIcon className="h-4 w-4 s3-explr-upload-icon" />*/}
                  {/*      Tải lên tập tin*/}
                  {/*    </>*/}
                  {/*  )}*/}
                  {/*</Button>*/}

                  {/*<Button*/}
                  {/*  variant="outline"*/}
                  {/*  size="sm"*/}
                  {/*  className="flex items-center gap-2 s3-explr-upload-folder-btn"*/}
                  {/*  onClick={() => {*/}
                  {/*    const input = document.createElement('input');*/}
                  {/*    input.type = 'file';*/}
                  {/*    input.multiple = true;*/}
                  {/*    input.webkitdirectory = true;*/}
                  {/*    input.onchange = (e) => {*/}
                  {/*      const files = (e.target as HTMLInputElement).files;*/}
                  {/*      if (files) {*/}
                  {/*        void uploadFiles(files, currentPrefix, 'folder');*/}
                  {/*      }*/}
                  {/*    };*/}
                  {/*    input.click();*/}
                  {/*  }}*/}
                  {/*  disabled={uploadType !== null || (Boolean(displayPrefixes) && !currentPrefix)}*/}
                  {/*>*/}
                  {/*  {uploadType === 'folder' ? (*/}
                  {/*    <>*/}
                  {/*      <Loader2 className="h-4 w-4 animate-spin mr-2 s3-explr-loader" />*/}
                  {/*      Đang tải lên...*/}
                  {/*    </>*/}
                  {/*  ) : (*/}
                  {/*    <>*/}
                  {/*      <FolderIcon className="h-4 w-4 s3-explr-folder-icon" />*/}
                  {/*      Tải lên thư mục*/}
                  {/*    </>*/}
                  {/*  )}*/}
                  {/*</Button>*/}

                  {currentJob ? (
                    <TooltipProvider delayDuration={0}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-2 s3-explr-add-website-btn"
                              disabled={true}
                            >
                              <Loader2 className="h-4 w-4 animate-spin s3-explr-loader" />
                              {currentJob.status ? (
                                <>
                                  {currentJob.status.isProcessing
                                    ? 'Đang thu thập '
                                    : 'Đang quét sitemap '}
                                  ({currentJob.status.completed}/{currentJob.status.total}) -{' '}
                                  {Math.floor(elapsedTime / 60)}:
                                  {String(elapsedTime % 60).padStart(2, '0')}
                                </>
                              ) : (
                                'Đang thu thập...'
                              )}
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => void stopCurrentJob()}
                              className="flex items-center gap-2"
                            >
                              <span className="sr-only">Dừng thu thập</span>
                              <StopIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </TooltipTrigger>
                        {currentJob.status && (
                          <TooltipContent side="right" sideOffset={5}>
                            <div className="space-y-1 p-2">
                              <p className="font-semibold">Đang thu thập {currentJob?.url}</p>
                              <p>
                                Tiến độ: {currentJob?.status.completed}/{currentJob?.status.total}{' '}
                                trang
                              </p>
                              <p>
                                Thời gian: {Math.floor(elapsedTime / 60)}:
                                {String(elapsedTime % 60).padStart(2, '0')}
                              </p>
                              <p>
                                Trạng thái:{' '}
                                {currentJob?.status.status === 'scraping'
                                  ? 'Đang thu thập'
                                  : currentJob?.status.status === 'completed'
                                    ? 'Hoàn thành'
                                    : 'Thất bại'}
                              </p>
                            </div>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    !idCreator && (
                      <Dialog open={isWebsiteDialogOpen} onOpenChange={setIsWebsiteDialogOpen}>
                        <DialogTrigger asChild>
                          {/*<Button*/}
                          {/*  variant="outline"*/}
                          {/*  size="sm"*/}
                          {/*  className="flex items-center gap-2 s3-explr-add-website-btn"*/}
                          {/*  disabled={Boolean(displayPrefixes) && !currentPrefix}*/}
                          {/*>*/}
                          {/*  <GlobeAltIcon className="h-4 w-4 s3-explr-globe-icon" />*/}
                          {/*  Thêm website*/}
                          {/*</Button>*/}
                        </DialogTrigger>
                        <DialogContent className="s3-explr-dialog-content">
                          <DialogHeader className="s3-explr-dialog-header">
                            <DialogTitle className="s3-explr-dialog-title">
                              Thêm website
                            </DialogTitle>
                            <DialogDescription className="s3-explr-dialog-description">
                              Nhập URL website để thu thập và thêm vào S3.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4 py-4 s3-explr-dialog-body">
                            <div className="space-y-2 s3-explr-input-group">
                              <Label htmlFor="websiteUrl" className="s3-explr-label">
                                URL Website
                              </Label>
                              <Input
                                id="websiteUrl"
                                value={websiteUrl}
                                onChange={(e) => setWebsiteUrl(e.target.value)}
                                placeholder="https://example.com"
                                type="url"
                                disabled={isCrawling}
                                className="s3-explr-input"
                              />
                            </div>
                            <div className="space-y-2 s3-explr-input-group">
                              <Label htmlFor="crawlLimit" className="s3-explr-label">
                                Giới hạn thu thập
                              </Label>
                              <Input
                                id="crawlLimit"
                                value={crawlLimit}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  if (/^\d*$/.test(value)) {
                                    // Only allow digits
                                    setCrawlLimit(value);
                                  }
                                }}
                                placeholder="1"
                                type="text"
                                disabled={isCrawling}
                                className="s3-explr-input"
                              />
                              <p className="text-sm text-gray-500">
                                Số lượng liên kết cần thu thập (mặc định: 1)
                              </p>
                            </div>
                          </div>
                          <DialogFooter className="s3-explr-dialog-footer">
                            <Button
                              variant="outline"
                              onClick={() => {
                                setIsWebsiteDialogOpen(false);
                                setWebsiteUrl('');
                                setCrawlLimit('1');
                              }}
                              disabled={isCrawling}
                              className="s3-explr-cancel-btn"
                            >
                              Hủy
                            </Button>
                            <Button
                              onClick={() => {
                                if (websiteUrl.trim()) {
                                  void crawlWebsite(websiteUrl.trim(), parseInt(crawlLimit) || 1);
                                }
                              }}
                              disabled={isCrawling || !websiteUrl.trim()}
                              className="s3-explr-crawl-btn"
                            >
                              {isCrawling ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                                  Đang thu thập...
                                </>
                              ) : (
                                'Bắt đầu thu thập'
                              )}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    )
                  )}

                  {currentPrefix.startsWith('BKTTW/') && (
                    <TooltipProvider delayDuration={0}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2 s3-explr-add-website-btn"
                            disabled={Boolean(displayPrefixes) && !currentPrefix}
                            onClick={handleExportReport}
                          >
                            {isLoadingHandleExportReport && (
                              <ArrowPathIcon
                                className={`h-4 w-4 s3-explr-refresh-icon ${isLoadingHandleExportReport ? 'animate-spin' : ''}`}
                              />
                            )}
                            Xuất báo cáo
                          </Button>
                        </TooltipTrigger>
                        {currentJob?.status && (
                          <TooltipContent side="right" sideOffset={5}>
                            <div className="space-y-1 p-2">
                              <p className="font-semibold">Đang thu thập {currentJob!.url}</p>
                              <p>
                                Tiến độ: {currentJob!.status.completed}/{currentJob!.status.total}{' '}
                                trang
                              </p>
                              <p>
                                Thời gian: {Math.floor(elapsedTime / 60)}:
                                {String(elapsedTime % 60).padStart(2, '0')}
                              </p>
                              <p>
                                Trạng thái:{' '}
                                {currentJob!.status.status === 'scraping'
                                  ? 'Đang thu thập'
                                  : currentJob!.status.status === 'completed'
                                    ? 'Hoàn thành'
                                    : 'Thất bại'}
                              </p>
                            </div>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <Dialog open={isWebsiteDialogOpen} onOpenChange={setIsWebsiteDialogOpen}>
                    <DialogTrigger asChild></DialogTrigger>
                    <DialogContent className="s3-explr-dialog-content">
                      <DialogHeader className="s3-explr-dialog-header">
                        <DialogTitle className="s3-explr-dialog-title">Thêm website</DialogTitle>
                        <DialogDescription className="s3-explr-dialog-description">
                          Nhập URL website để thu thập và thêm vào S3.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4 s3-explr-dialog-body">
                        <div className="space-y-2 s3-explr-input-group">
                          <Label htmlFor="websiteUrl" className="s3-explr-label">
                            URL Website
                          </Label>
                          <Input
                            id="websiteUrl"
                            value={websiteUrl}
                            onChange={(e) => setWebsiteUrl(e.target.value)}
                            placeholder="https://example.com"
                            type="url"
                            disabled={isCrawling}
                            className="s3-explr-input"
                          />
                        </div>
                        <div className="space-y-2 s3-explr-input-group">
                          <Label htmlFor="crawlLimit" className="s3-explr-label">
                            Giới hạn thu thập
                          </Label>
                          <Input
                            id="crawlLimit"
                            value={crawlLimit}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (/^\d*$/.test(value)) {
                                // Only allow digits
                                setCrawlLimit(value);
                              }
                            }}
                            placeholder="1"
                            type="text"
                            disabled={isCrawling}
                            className="s3-explr-input"
                          />
                          <p className="text-sm text-gray-500">
                            Số lượng liên kết cần thu thập (mặc định: 1)
                          </p>
                        </div>
                      </div>
                      <DialogFooter className="s3-explr-dialog-footer">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsWebsiteDialogOpen(false);
                            setWebsiteUrl('');
                            setCrawlLimit('1');
                          }}
                          disabled={isCrawling}
                          className="s3-explr-cancel-btn"
                        >
                          Hủy
                        </Button>
                        <Button
                          onClick={() => {
                            if (websiteUrl.trim()) {
                              void crawlWebsite(websiteUrl.trim(), parseInt(crawlLimit) || 1);
                            }
                          }}
                          disabled={isCrawling || !websiteUrl.trim()}
                          className="s3-explr-crawl-btn"
                        >
                          {isCrawling ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                              Đang thu thập...
                            </>
                          ) : (
                            'Bắt đầu thu thập'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}

              {currentPrefix.startsWith('BKTTW/') && (
                <TooltipProvider delayDuration={0}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 s3-explr-add-website-btn"
                        disabled={Boolean(displayPrefixes) && !currentPrefix}
                        onClick={handleExportReport}
                      >
                        {isLoadingHandleExportReport && (
                          <ArrowPathIcon
                            className={`h-4 w-4 s3-explr-refresh-icon ${isLoadingHandleExportReport ? 'animate-spin' : ''}`}
                          />
                        )}
                        Xuất báo cáo
                      </Button>
                    </TooltipTrigger>
                    {isLoadingHandleExportReport && (
                      <TooltipContent sideOffset={5}>Đang xuất báo cáo</TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              )}

              <div className="flex items-center gap-4">
                {showOneDriveModal && (
                  <Modal.Root open={showOneDriveModal} onOpenChange={setShowOneDriveModal}>
                    <Modal.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-50" />
                    <Modal.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[700px] max-w-[90vw] max-h-[80vh] z-50 flex flex-col">
                      <div className="relative">
                        <Modal.Close asChild>
                          <button
                            className="absolute top-2 right-2 text-gray-600"
                            onClick={handleCloseOneDriveModal}
                          >
                            ✖
                          </button>
                        </Modal.Close>
                        <h2 className="text-lg font-semibold">My OneDrive</h2>
                        <Breadcrumb className="onedrive-breadcrumb">
                          <BreadcrumbList className="onedrive-breadcrumb-list">
                            {breadcrumbOneDrive.map((item, index) => (
                              <div key={index} className="flex items-center">
                                {index > 0 && (
                                  <BreadcrumbSeparator className="onedrive-breadcrumb-separator" />
                                )}
                                <BreadcrumbItem className="onedrive-breadcrumb-item">
                                  <BreadcrumbLink
                                    onClick={() => handleOneDriveFolderNavigation(index)}
                                    className="cursor-pointer onedrive-breadcrumb-link"
                                  >
                                    {item.name}
                                  </BreadcrumbLink>
                                </BreadcrumbItem>
                              </div>
                            ))}
                          </BreadcrumbList>
                        </Breadcrumb>
                      </div>
                      <div className="space-y-2 mt-4 flex-1 overflow-y-auto">
                        {isLoadingOneDrive ? (
                          <div className="text-sm text-gray-500">
                            Đang tải nội dung thư mục... Vui lòng đợi.
                          </div>
                        ) : (
                          <>
                            {[...folders, ...files].length > 0 ? (
                              [...folders, ...files].map((item) => (
                                <div
                                  key={item.id}
                                  className={`flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 ${
                                    selectedOneDriveItems.some((i) => i.id === item.id)
                                      ? 'bg-blue-100'
                                      : ''
                                  }`}
                                >
                                  <input
                                    type="checkbox"
                                    checked={selectedOneDriveItems.some((i) => i.id === item.id)}
                                    onChange={(e) => {
                                      e.stopPropagation();
                                      handleOneDriveSelectItem(item);
                                    }}
                                    className="cursor-pointer"
                                  />

                                  {item.isFolder ? (
                                    <FolderIcon
                                      className="text-blue-500 w-5 h-5 cursor-pointer"
                                      onClick={() => fetchOneDriveFiles(item.id, item.name)}
                                    />
                                  ) : (
                                    <ArrowUpTrayIcon className="text-gray-500 w-5 h-5" />
                                  )}

                                  <div className="flex-1">
                                    <div
                                      className={`font-medium ${item.isFolder ? 'cursor-pointer text-blue-600' : ''}`}
                                      onClick={() => {
                                        if (item.isFolder) {
                                          fetchOneDriveFiles(item.id, item.name);
                                        } else {
                                          handleOneDriveSelectItem(item);
                                        }
                                      }}
                                    >
                                      {item.name}
                                    </div>

                                    {!item.isFolder && (
                                      <div className="text-xs text-gray-500">
                                        {formatBytes(item.size)} •{' '}
                                        {formatDate(item.lastModified.toISOString())}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))
                            ) : (
                              <div className="text-sm text-gray-500">
                                Không có thư mục hoặc tệp nào.
                              </div>
                            )}
                          </>
                        )}
                      </div>

                      <div className="mt-4 flex flex-wrap justify-end gap-2">
                        <Button
                          onClick={() => {
                            syncOneDriveToS3(selectedOneDriveItems, currentPrefix);
                            handleCloseOneDriveModal();
                          }}
                          className="bg-[#1682fe] hover:bg-[#0f6cd4] text-white rounded-md px-4 py-2 transition-colors"
                          disabled={selectedOneDriveItems.length === 0}
                        >
                          Đồng bộ
                        </Button>
                      </div>
                      <button
                        onClick={() => setIsOneDriveLogoutConfirmOpen(true)}
                        className="absolute bottom-4 left-4 text-gray-600 hover:text-gray-800"
                        title="Đăng xuất OneDrive"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                          <polyline points="16 17 21 12 16 7" />
                          <line x1="21" y1="12" x2="9" y2="12" />
                        </svg>
                      </button>
                    </Modal.Content>
                  </Modal.Root>
                )}

                <Modal.Root
                  open={isOneDriveLogoutConfirmOpen}
                  onOpenChange={setIsOneDriveLogoutConfirmOpen}
                >
                  <Modal.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-[60]" />
                  <Modal.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[400px] max-w-[90vw] z-[60]">
                    <h3 className="text-lg font-semibold mb-4">Xác nhận đăng xuất</h3>
                    <p className="text-gray-600 mb-6">
                      Bạn có chắc chắn muốn đăng xuất khỏi OneDrive?
                    </p>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setIsOneDriveLogoutConfirmOpen(false)}
                      >
                        Hủy
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => {
                          logoutOneDrive();
                          setIsOneDriveLogoutConfirmOpen(false);
                        }}
                      >
                        Đăng xuất
                      </Button>
                    </div>
                  </Modal.Content>
                </Modal.Root>
                {isSharePointModalOpen && (
                  <Modal.Root open={isSharePointModalOpen} onOpenChange={setIsSharePointModalOpen}>
                    <Modal.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-50" />
                    <Modal.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[700px] max-w-[90vw] max-h-[80vh] z-50 flex flex-col">
                      <div className="relative">
                        <Modal.Close asChild>
                          <button
                            className="absolute top-2 right-2 text-gray-600"
                            onClick={handleCloseSharePointModal}
                          >
                            ✖
                          </button>
                        </Modal.Close>
                        <div className="flex items-center gap-2">
                          {selectedSite && (
                            <button
                              onClick={() => setSelectedSite(null)}
                              className="text-gray-600 hover:text-gray-800"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <path d="M19 12H5M12 19l-7-7 7-7" />
                              </svg>
                            </button>
                          )}
                          <h2 className="text-lg font-semibold">My SharePoint</h2>
                        </div>
                        {selectedSite && (
                          <Breadcrumb className="sharepoint-breadcrumb">
                            <BreadcrumbList className="sharepoint-breadcrumb-list">
                              {sharePointBreadcrumbs.map((item, index) => (
                                <div key={index} className="flex items-center">
                                  {index > 0 && (
                                    <BreadcrumbSeparator className="sharepoint-breadcrumb-separator" />
                                  )}
                                  <BreadcrumbItem className="sharepoint-breadcrumb-item">
                                    <BreadcrumbLink
                                      onClick={() => handleSharePointFolderNavigation(index)}
                                      className="cursor-pointer sharepoint-breadcrumb-link"
                                    >
                                      {item.name}
                                    </BreadcrumbLink>
                                  </BreadcrumbItem>
                                </div>
                              ))}
                            </BreadcrumbList>
                          </Breadcrumb>
                        )}
                      </div>

                      {/* Vùng danh sách file/folder có thể cuộn */}
                      <div className="space-y-2 mt-4 flex-1 overflow-y-auto">
                        {isLoadingSharePoint ? (
                          <div className="text-sm text-gray-500">
                            Đang tải nội dung thư mục... Vui lòng đợi.
                          </div>
                        ) : selectedSite ? (
                          [...sharePointFolders, ...sharePointFiles].length === 0 ? (
                            <div className="text-sm text-gray-500">
                              Không có thư mục hoặc tệp nào.
                            </div>
                          ) : (
                            [...sharePointFolders, ...sharePointFiles].map((item) => {
                              const isSelected = selectedSharePointItems.some(
                                (i) => i.id === item.id,
                              );

                              return (
                                <div
                                  key={item.id}
                                  className={`flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 ${
                                    isSelected ? 'bg-blue-100' : ''
                                  }`}
                                >
                                  <input
                                    type="checkbox"
                                    checked={isSelected}
                                    onChange={(e) => {
                                      e.stopPropagation();
                                      handleSharePointSelectItem(item);
                                    }}
                                    className="cursor-pointer"
                                  />
                                  {item.isFolder ? (
                                    <FolderIcon
                                      className="text-blue-500 w-5 h-5 cursor-pointer"
                                      onClick={() =>
                                        fetchSharePointFiles(
                                          item.id,
                                          item.name,
                                          selectedSite?.hostname || '',
                                          selectedSite?.sitePath || '',
                                        )
                                      }
                                    />
                                  ) : (
                                    <ArrowUpTrayIcon className="text-gray-500 w-5 h-5" />
                                  )}
                                  <div className="flex-1">
                                    <div
                                      className={`font-medium ${item.isFolder ? 'cursor-pointer text-blue-600' : ''}`}
                                      onClick={() => {
                                        if (item.isFolder) {
                                          fetchSharePointFiles(
                                            item.id,
                                            item.name,
                                            selectedSite?.hostname || '',
                                            selectedSite?.sitePath || '',
                                          );
                                        }
                                      }}
                                    >
                                      {item.name}
                                    </div>
                                    {!item.isFolder && (
                                      <div className="text-xs text-gray-500">
                                        {formatBytes(item.size)} •{' '}
                                        {item.lastModified.toLocaleString()}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })
                          )
                        ) : (
                          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                            {sharePointSites.map((site) => (
                              <div
                                key={site.id}
                                className="cursor-pointer bg-white shadow-md rounded-xl p-5 flex flex-col justify-center items-start transition-shadow duration-200 hover:shadow-lg border border-gray-100 min-h-[120px]"
                                style={{
                                  overflow: 'hidden',
                                  wordBreak: 'break-word',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'normal',
                                }}
                                onClick={() => handleSelectSite(site)}
                              >
                                <div
                                  className="font-bold text-lg mb-1 truncate w-full"
                                  title={site.name}
                                >
                                  {site.name}
                                </div>
                                <div
                                  className="text-sm text-gray-500 truncate w-full"
                                  title={site.description}
                                >
                                  {site.description}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Các nút hành động ở dưới cùng */}
                      <div className="mt-4 flex flex-wrap justify-end gap-2">
                        {selectedSite && (
                          <>
                            <Button
                              onClick={() => {
                                syncSharePointToS3(selectedSharePointItems, currentPrefix);
                                handleCloseSharePointModal();
                              }}
                              className="bg-[#1682fe] hover:bg-[#0f6cd4] text-white rounded-md px-4 py-2 transition-colors"
                              disabled={selectedSharePointItems.length === 0}
                            >
                              Đồng bộ
                            </Button>
                            <Button onClick={() => setSelectedSite(null)}>Quay lại</Button>
                          </>
                        )}
                      </div>
                      <button
                        onClick={() => setIsSharePointLogoutConfirmOpen(true)}
                        className="absolute bottom-4 left-4 text-gray-600 hover:text-gray-800"
                        title="Đăng xuất SharePoint"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                          <polyline points="16 17 21 12 16 7" />
                          <line x1="21" y1="12" x2="9" y2="12" />
                        </svg>
                      </button>
                    </Modal.Content>
                  </Modal.Root>
                )}

                {/* SharePoint Logout Confirmation Modal */}
                <Modal.Root
                  open={isSharePointLogoutConfirmOpen}
                  onOpenChange={setIsSharePointLogoutConfirmOpen}
                >
                  <Modal.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-[60]" />
                  <Modal.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[400px] max-w-[90vw] z-[60]">
                    <h3 className="text-lg font-semibold mb-4">Xác nhận đăng xuất</h3>
                    <p className="text-gray-600 mb-6">
                      Bạn có chắc chắn muốn đăng xuất khỏi SharePoint?
                    </p>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setIsSharePointLogoutConfirmOpen(false)}
                      >
                        Hủy
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => {
                          logoutSharePoint();
                          setIsSharePointLogoutConfirmOpen(false);
                        }}
                      >
                        Đăng xuất
                      </Button>
                    </div>
                  </Modal.Content>
                </Modal.Root>
              </div>

              <Dialog
                open={!!showInfoFlowsDialogs}
                onOpenChange={(open) => {
                  if (!open) {
                    setShowInfoFlowsDialogs(false);
                    setDataFlowModels([]);
                    setFolderToShowFlow('');
                  }
                }}
              >
                <DialogContent className="max-h-[700px] pb-3 px-1 s3-explr-input-group max-w-5xl w-[90%] p-6 bg-white rounded-lg shadow-lg">
                  <DialogHeader className="s3-explr-dialog-header">
                    <DialogTitle className="s3-explr-dialog-title">
                      <h3 className="font-medium mb-2">
                        Danh sách flows đang sử dụng dữ liệu từ folder(
                        <span className="font-bold">{folderToShowFlow}</span>):{' '}
                      </h3>
                    </DialogTitle>
                    <DialogDescription className="s3-explr-dialog-description">
                      {loadingFlowsDialogs ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-6 w-6 animate-spin" />
                          <span className="ml-2">Đang tải thông tin flows...</span>
                        </div>
                      ) : (
                        <div>
                          {dataFlowModels &&
                          Array.isArray(dataFlowModels) &&
                          dataFlowModels.length > 0 ? (
                            <div className="max-h-[400px] overflow-y-auto">
                              <table className="w-full border-collapse">
                                <thead>
                                  <tr className="bg-gray-100">
                                    <th className="border border-gray-300 px-4 py-2 text-left">
                                      Tên Flow
                                    </th>
                                    <th className="border border-gray-300 px-4 py-2 text-left">
                                      KnowledgeBase Id
                                    </th>
                                    <th className="border border-gray-300 px-4 py-2 text-left">
                                      Loại
                                    </th>
                                    <th className="border border-gray-300 px-4 py-2 text-left">
                                      Ngày tạo
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {dataFlowModels.map((flow: any, index: number) => (
                                    <tr
                                      key={index}
                                      className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                                    >
                                      <td className="border border-gray-300 px-4 py-2">
                                        {flow.name}
                                      </td>
                                      <td className="border border-gray-300 px-4 py-2">
                                        {flow.KnowledgeBaseId}
                                      </td>
                                      <td className="border border-gray-300 px-4 py-2">
                                        {flow.type === 'MULTIAGENT' ? 'Agent Flow' : 'Chat Flow'}
                                      </td>
                                      <td className="border border-gray-300 px-4 py-2">
                                        {flow.createdDate
                                          ? new Date(flow.createdDate).toLocaleString('vi-VN')
                                          : 'N/A'}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          ) : (
                            <div className="text-gray-500 italic py-2">
                              Không có flow nào sử dụng dữ liệu từ folder này
                            </div>
                          )}
                        </div>
                      )}
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter className="s3-explr-dialog-footer">
                    <Button
                      variant="outline"
                      onClick={() => setFileToDelete(null)}
                      className="s3-explr-cancel-btn"
                    >
                      Hủy
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => handleDelete(fileToDelete!)}
                      disabled={isDeleting}
                      className="s3-explr-confirm-delete-btn"
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                          Đang xóa...
                        </>
                      ) : (
                        'Xóa'
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog open={!!fileToDelete} onOpenChange={(open) => !open && setFileToDelete(null)}>
                <DialogContent className="s3-explr-dialog-content">
                  <DialogHeader className="s3-explr-dialog-header">
                    <DialogTitle className="s3-explr-dialog-title">Xác nhận xóa</DialogTitle>
                    <DialogDescription className="s3-explr-dialog-description">
                      Bạn có chắc chắn muốn xóa {fileToDelete?.name}? Hành động này không thể hoàn
                      tác.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter className="s3-explr-dialog-footer">
                    <Button
                      variant="outline"
                      onClick={() => setFileToDelete(null)}
                      className="s3-explr-cancel-btn"
                    >
                      Hủy
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => handleDelete(fileToDelete!)}
                      disabled={isDeleting}
                      className="s3-explr-confirm-delete-btn"
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                          Đang xóa...
                        </>
                      ) : (
                        'Xóa'
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog
                open={!!fileToRename}
                onOpenChange={(open) => {
                  if (!open) {
                    setFileToRename(null);
                    setMetadataForEdit(null);
                    setNewName('');
                    setFileContent('');
                    setActiveTab('rename');
                  }
                }}
              >
                <DialogContent className="s3-explr-dialog-content max-w-5xl w-[90%]">
                  <DialogHeader className="s3-explr-dialog-header">
                    <DialogTitle className="s3-explr-dialog-title">
                      {fileToRename?.isFolder
                        ? 'Đổi tên thư mục'
                        : isTextFile(fileToRename!)
                          ? 'Quản lý tập tin'
                          : 'Đổi tên tập tin'}
                    </DialogTitle>
                  </DialogHeader>
                  {fileToRename && (
                    <Tabs
                      value={activeTab}
                      onValueChange={(value: any) =>
                        setActiveTab(value as 'rename' | 'edit' | 'edit-metadata')
                      }
                    >
                      <TabsList
                        aria-checked={!isTextFile(fileToRename)}
                        aria-disabled={fileToRename.isFolder}
                        className="grid w-full grid-cols-3 aria-checked:grid-cols-2  aria-disabled:grid-cols-1"
                      >
                        <TabsTrigger value="rename">Đổi tên</TabsTrigger>
                        {!fileToRename.isFolder && (
                          <>
                            {isTextFile(fileToRename) && (
                              <TabsTrigger value="edit">Sửa nội dung</TabsTrigger>
                            )}
                            <TabsTrigger value="edit-metadata">Sửa metadata file</TabsTrigger>
                          </>
                        )}
                      </TabsList>
                      <TabsContent value="rename">
                        <div className="space-y-4 py-4 s3-explr-dialog-body">
                          <div className="h-[70dvh] min-h-[500px] overflow-hidden overflow-y-auto pb-3 px-1 s3-explr-input-group">
                            <Label htmlFor="newName" className="s3-explr-label">
                              Tên mới
                            </Label>
                            <Input
                              id="newName"
                              value={newName}
                              onChange={(e) => setNewName(e.target.value)}
                              placeholder="Nhập tên mới"
                              disabled={isRenaming}
                              className="s3-explr-input"
                            />
                          </div>
                        </div>
                      </TabsContent>
                      {!fileToRename.isFolder && (
                        <>
                          {isTextFile(fileToRename) && (
                            <TabsContent value="edit">
                              <div className="space-y-4 py-4">
                                {isLoadingContent ? (
                                  <div className="flex items-center justify-center py-4">
                                    <Loader2 className="h-6 w-6 animate-spin" />
                                  </div>
                                ) : (
                                  <div className="h-[70dvh] min-h-[500px] overflow-hidden overflow-y-auto pb-3 px-1">
                                    <Label htmlFor="content" className="s3-explr-label">
                                      Nội dung tập tin
                                    </Label>
                                    <Textarea
                                      id="content"
                                      value={fileContent}
                                      onChange={(e: any) => setFileContent(e.target.value)}
                                      placeholder="Nhập nội dung tập tin"
                                      className="min-h-[200px] font-mono"
                                      disabled={isSavingContent}
                                    />
                                  </div>
                                )}
                              </div>
                            </TabsContent>
                          )}
                          <TabsContent value="edit-metadata">
                            <div className="space-y-4 py-4">
                              {isLoadingContent ? (
                                <div className="flex items-center justify-center py-4">
                                  <Loader2 className="h-6 w-6 animate-spin" />
                                </div>
                              ) : (
                                <div className="h-[70dvh] min-h-[500px] overflow-hidden overflow-y-auto pb-3 px-1">
                                  {metadataForEdit &&
                                    typeof metadataForEdit === 'object' &&
                                    Object.entries(metadataForEdit).map(([key, value], index) => (
                                      <div key={index} className="pb-3 flex flex-col gap-2">
                                        <Label
                                          htmlFor={key}
                                          className="s3-explr-label flex items-center gap-1"
                                        >
                                          {keyMapMetadata[key] || key}
                                          {isDisableField(key) && (
                                            <PenOff className="h-[12px] w-[12px] text-red-500" />
                                          )}
                                        </Label>

                                        {isTextarea(key) ? (
                                          <Textarea
                                            id={key}
                                            value={
                                              typeof value === 'string'
                                                ? value
                                                : JSON.stringify(value)
                                            }
                                            onChange={(e: any) => {
                                              const newMetadata = {
                                                ...metadataForEdit,
                                                [key]: e.target.value,
                                              };
                                              setMetadataForEdit(newMetadata);
                                            }}
                                            placeholder="Nhập nội dung tập tin"
                                            className="min-h-[200px] font-mono"
                                            disabled={isSavingContent || isDisableField(key)}
                                          />
                                        ) : (
                                          <Input
                                            id={key}
                                            value={
                                              typeof value === 'string'
                                                ? value
                                                : JSON.stringify(value)
                                            }
                                            onChange={(e: any) => {
                                              const newMetadata = {
                                                ...metadataForEdit,
                                                [key]: e.target.value,
                                              };
                                              setMetadataForEdit(newMetadata);
                                            }}
                                            placeholder="Nhập nội dung"
                                            className="s3-explr-input"
                                            disabled={isSavingContent || isDisableField(key)}
                                          />
                                        )}
                                      </div>
                                    ))}
                                </div>
                              )}
                            </div>
                          </TabsContent>
                        </>
                      )}
                    </Tabs>
                  )}
                  <DialogFooter className="s3-explr-dialog-footer">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setFileToRename(null);
                        setMetadataForEdit(null);
                        setNewName('');
                        setFileContent('');
                      }}
                      className="s3-explr-cancel-btn"
                    >
                      Hủy
                    </Button>
                    {activeTab === 'edit' ? (
                      <Button
                        onClick={() => fileToRename && saveFileContent(fileToRename, fileContent)}
                        disabled={isSavingContent || isLoadingContent}
                        className="s3-explr-save-btn"
                      >
                        {isSavingContent ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                            Đang lưu...
                          </>
                        ) : (
                          'Lưu thay đổi'
                        )}
                      </Button>
                    ) : activeTab === 'edit-metadata' ? (
                      <Button
                        onClick={() =>
                          metadataForEdit &&
                          typeof metadataForEdit === 'object' &&
                          fileToRename &&
                          saveJsonFile(fileToRename)
                        }
                        disabled={isSavingContent || isLoadingContent}
                        className="s3-explr-save-btn"
                      >
                        {isSavingContent ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                            Đang lưu...
                          </>
                        ) : (
                          'Lưu thay đổi'
                        )}
                      </Button>
                    ) : (
                      <Button
                        onClick={() => {
                          if (newName.trim() && fileToRename) {
                            void handleRename(fileToRename, newName.trim());
                          }
                        }}
                        disabled={
                          isRenaming || !newName.trim() || newName.trim() === fileToRename?.name
                        }
                        className="s3-explr-rename-btn"
                      >
                        {isRenaming ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin s3-explr-loader" />
                            Đang đổi tên...
                          </>
                        ) : (
                          'Đổi tên'
                        )}
                      </Button>
                    )}
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {fileToMove && openDialogRename && (
                <DialogRemoveFiles
                  openDialogRename={openDialogRename}
                  setOpenDialogRename={setOpenDialogRename}
                  apiBaseUrl={apiBaseUrl}
                  setFileToMove={setFileToMove}
                  fileToMove={fileToMove}
                  refreshFiles={refreshFiles}
                  ignoreCache={ignoreCache}
                />
              )}
            </>
          )}
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative s3-explr-error">
            Đã xảy ra lỗi khi tải danh sách tập tin
          </div>
        )}

        <FileUpload
          onFilesSelected={(e: any, typeUpload) => uploadFiles(e, currentPrefix, typeUpload)}
        >
          <div className="rounded-md border s3-explr-table-container">
            <Table className="s3-explr-table">
              <TableHeader className="s3-explr-table-header">
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow
                    key={headerGroup.id}
                    className="hover:bg-transparent s3-explr-header-row"
                  >
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id} className="s3-explr-header-cell">
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody className="s3-explr-table-body">
                {table.getRowModel().rows.length === 0 ? (
                  <TableRow className="s3-explr-empty-row">
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center s3-explr-empty-cell"
                    >
                      {searchQuery ? (
                        <div className="text-gray-500 s3-explr-no-results">
                          Không tìm thấy tập tin hoặc thư mục nào khớp với "{searchQuery}"
                        </div>
                      ) : (
                        <div className="text-gray-500 s3-explr-empty-message">
                          {isLoading
                            ? 'Đang tải nội dung thư mục... Vui lòng đợi'
                            : 'Thư mục này không chứa tập tin hoặc thư mục con nào'}
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id} className="h-12 s3-explr-row">
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="align-middle s3-explr-cell">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </FileUpload>

        {searchQuery && estimatedTotalHits !== 0 && (
          <>
            <Pagination>
              <PaginationContent>
                <PaginationPrevious
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  aria-disabled={currentPage === 1}
                  className="cursor-pointer aria-disabled:pointer-events-none"
                />
                {Array.from({ length: Math.ceil(estimatedTotalHits / 20) }, (_, index) => {
                  const pageIndex = index + 1;
                  const totalPages = Math.ceil(estimatedTotalHits / 20);
                  const isHidden =
                    totalPages > 7 &&
                    !(
                      pageIndex === 1 ||
                      pageIndex === totalPages ||
                      (pageIndex >= currentPage - 1 && pageIndex <= currentPage + 1)
                    );

                  if (isHidden) {
                    if (
                      (pageIndex === currentPage - 2 && currentPage > 4) ||
                      (pageIndex === currentPage + 2 && currentPage < totalPages - 3)
                    ) {
                      return (
                        <PaginationEllipsis key={index} className="pointer-events-none">
                          ...
                        </PaginationEllipsis>
                      );
                    }
                    return null;
                  }

                  return (
                    <PaginationItem className="cursor-pointer" key={index}>
                      <PaginationLink
                        isActive={currentPage === pageIndex}
                        onClick={() => setCurrentPage(pageIndex)}
                        className={`${
                          currentPage === pageIndex
                            ? 'bg-blue-500 text-white'
                            : 'bg-white text-black'
                        } hover:bg-blue-100`}
                      >
                        {pageIndex}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                <PaginationItem>
                  <PaginationNext
                    onClick={() =>
                      setCurrentPage((prev) =>
                        Math.min(prev + 1, Math.ceil(estimatedTotalHits / 20)),
                      )
                    }
                    aria-disabled={currentPage === Math.ceil(estimatedTotalHits / 20)}
                    className="cursor-pointer aria-disabled:pointer-events-none"
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </>
        )}
        {asSelector && onSelected && (
          <div className="flex justify-between items-center mt-4 s3-explr-selector-footer">
            <div className="text-sm text-gray-500 s3-explr-selection-count">
              {calculateSelectedFiles()}
            </div>
            <Button
              variant="default"
              onClick={() => onSelected(getSelectedFiles())}
              disabled={selectedKeys.size === 0}
              className="s3-explr-ok-btn"
            >
              OK
            </Button>
          </div>
        )}
        <DialogShare
          open={!!fileToShare}
          onOpenChange={(open) => !open && setFileToShare(null)}
          fileName={fileToShare?.name || ''}
        />
      </div>
    );
  },
);

export const Home = (props: any) => {
  return (
    <GoogleOAuthProvider clientId={CLIENT_ID}>
      <HomeWithoutContext
        {...props}
        // asSelector={true}
        // preSelectedFiles={['PO/', 'abs-sample/FAQs.txt']}
      />
    </GoogleOAuthProvider>
  );
};

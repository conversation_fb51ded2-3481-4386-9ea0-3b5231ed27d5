// Original postgres.ts - now with automatic audit logging
// This file has been enhanced to automatically log all CRUD operations
// The original functionality remains exactly the same for backward compatibility

import { Pool } from 'pg';
import { auditContext } from './audit-context';
import { logAuditEntryAsync } from './background-logger';
import { SQLAnalyzer } from './sql-analyzer';
import { auditConfig, logDebug, logError } from './audit-config';
import type { AuditLogEntry } from '../types/audit';

export const pool = new Pool({
  user: process.env.DATABASE_USER,
  host: process.env.DATABASE_HOST,
  database: process.env.DATABASE_NAME,
  password: process.env.DATABASE_PASSWORD,
  port: Number(process.env.DATABASE_PORT) || 5432,
});

export const connect = async () => {
  try {
    await pool.connect();
    console.log('Connected to the database');
  } catch (err) {
    console.error('Error connecting to the database', err);
    throw err;
  }
};

export const retryConnect = async (retries = 5) => {
  while (retries) {
    try {
      await connect();
      break;
    } catch (err) {
      retries -= 1;
      console.log(`Retries left: ${retries}`);
      if (!retries) throw err;
      await new Promise((res) => setTimeout(res, 5000));
    }
  }
};

// Enhanced logging function - runs asynchronously to not impact performance
async function logOperation(
  query: string,
  values: any[],
  result: any,
  error?: any,
  executionTime?: number
): Promise<void> {
  console.log('--- [AUDIT LOG] --- STEP 1: logOperation triggered ---');
  console.log("QUERY TEST",query)
  try {
    // Check if audit logging is enabled
    if (!auditConfig.isEnabled()) {
      console.log('[AUDIT LOG] Logging is disabled. Skipping.');
      return;
    }

    // Only log if we have audit context (from middleware)
    if (!auditContext.hasContext()) {
      logDebug('No audit context available, skipping logging');
      console.log('[AUDIT LOG] No audit context. Skipping.');
      return;
    }

    const sqlOperation = SQLAnalyzer.parseSQL(query);
    console.log('[AUDIT LOG] STEP 2: SQL Parsed', { sqlOperation });
    if (!sqlOperation) {
      logDebug('Could not parse SQL operation, skipping logging');
      return;
    }

    // Check configuration-based skip conditions
    if (auditConfig.shouldSkipTable(sqlOperation.table)) {
      logDebug(`Skipping audit log for table: ${sqlOperation.table}`);
      return;
    }

    if (auditConfig.shouldSkipOperation(sqlOperation.type)) {
      logDebug(`Skipping audit log for operation: ${sqlOperation.type}`);
      return;
    }

    // Skip READ operations if not configured to log them
    if (sqlOperation.type === 'SELECT' && !auditConfig.shouldLogReadOperations()) {
      logDebug('Skipping READ operation logging (disabled in config)');
      return;
    }

    // Additional skip check from SQLAnalyzer
    if (SQLAnalyzer.shouldSkipAuditLogging(sqlOperation)) {
      return;
    }

    const auditInfo = SQLAnalyzer.extractAuditInfo(sqlOperation, values, result);
    const auditOptions = auditContext.getAuditOptions();
    console.log('[AUDIT LOG] STEP 3: Extracted Audit Info & Options', { auditInfo, auditOptions });

    let changes: Record<string, any> | undefined;

    if (sqlOperation.type === 'INSERT') {
      const data = SQLAnalyzer.extractDataFromValues(sqlOperation, values);
      changes = {
        after: data,
        fields_changed: Object.keys(data),
      };
    } else if (sqlOperation.type === 'UPDATE') {
      const beforeData = await getBeforeUpdateData(sqlOperation, values);
      const afterData = SQLAnalyzer.extractDataFromValues(sqlOperation, values);

      if (beforeData) {
        changes = calculateChanges(beforeData, afterData);
      } else {
        changes = {
          after: afterData,
          fields_changed: Object.keys(afterData),
        };
      }
    } else if (sqlOperation.type === 'DELETE') {
      const beforeData = await getBeforeUpdateData(sqlOperation, values);
      if (beforeData) {
        changes = {
          before: beforeData,
          fields_changed: Object.keys(beforeData),
        };
      }
    }

    const auditEntry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'> = {
      action: auditInfo.operation_type,
      target_type: auditInfo.target_type,
      target_id: auditInfo.target_id,
      target_name: auditInfo.target_name,
      username: auditOptions.username,
      user_id: auditOptions.user_id,
      changes,
      metadata: {
        ...auditOptions.metadata,
        table: sqlOperation.table,
        execution_time_ms: executionTime,
        query_type: sqlOperation.type,
        has_where_clause: !!sqlOperation.whereClause,
        has_returning_clause: !!sqlOperation.returningClause,
        is_slow_query: executionTime ? auditConfig.isSlowQuery(executionTime) : false,
      },
      ip_address: auditOptions.ip_address,
      user_agent: auditOptions.user_agent,
      session_id: auditOptions.session_id,
      error_message: error?.message,
      success: !error,
    };
    console.log('[AUDIT LOG] STEP 4: Created Audit Entry', { auditEntry });

    // Log asynchronously in background
    await logAuditEntryAsync(auditEntry);

    logDebug(`Logged ${auditInfo.operation_type} operation on ${auditInfo.target_type}`, {
      target_id: auditInfo.target_id,
      execution_time: executionTime,
      success: !error,
    });
  } catch (logError: any) {
    // Never let audit logging break the main operation
    logError('Failed to log audit entry:', logError);
  }
}

async function getBeforeUpdateData(sqlOperation: any, values: any[]): Promise<Record<string, any> | null> {
  try {
    if (!sqlOperation.whereClause || !values.length) {
      return null;
    }

    const selectQuery = `SELECT * FROM ${sqlOperation.table} WHERE ${sqlOperation.whereClause}`;
    const result = await pool.query(selectQuery, values.slice(-1));
    return result.rows[0] || null;
  } catch (error) {
    console.debug('Could not fetch before data:', error);
    return null;
  }
}

function calculateChanges(before: Record<string, any>, after: Record<string, any>): Record<string, any> {
  const changes: Record<string, any> = {
    before: {},
    after: {},
    fields_changed: [],
  };

  const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

  for (const key of Array.from(allKeys)) {
    const beforeValue = before[key];
    const afterValue = after[key];

    if (!isEqual(beforeValue, afterValue)) {
      changes.fields_changed.push(key);
      changes.before[key] = beforeValue;
      changes.after[key] = afterValue;
    }
  }

  return changes;
}

function isEqual(a: any, b: any): boolean {
  if (a === b) return true;
  if (a == null || b == null) return a === b;
  if (typeof a !== typeof b) return false;
  if (typeof a === 'object') {
    return JSON.stringify(a) === JSON.stringify(b);
  }
  return false;
}

export const add = async (query: string, values: any[]) => {
  console.log('--- [AUDIT LOG] --- CRUD Operation: add ---');
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;

    // Log asynchronously without blocking
    setImmediate(() => {
      logOperation(query, values, res.rows, undefined, executionTime);
    });

    return res.rows;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing add query', err);

    // Log error asynchronously
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });

    throw err;
  }
};

export const update = async (query: string, values: any[]) => {
  console.log('--- [AUDIT LOG] --- CRUD Operation: update ---');
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;

    // Log asynchronously without blocking
    setImmediate(() => {
      logOperation(query, values, res, undefined, executionTime);
    });

    return res;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing update query', err);

    // Log error asynchronously
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });

    throw err;
  }
};

export const remove = async (query: string, values: any[]) => {
  console.log('--- [AUDIT LOG] --- CRUD Operation: remove ---');
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;

    // Log asynchronously without blocking
    setImmediate(() => {
      logOperation(query, values, res.rowCount, undefined, executionTime);
    });

    return res.rowCount;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing delete query', err);

    // Log error asynchronously
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });

    throw err;
  }
};

export const get = async (query: string, values: any[]) => {
  console.log('--- [AUDIT LOG] --- CRUD Operation: get ---');
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;

    // Log asynchronously without blocking
    setImmediate(() => {
      logOperation(query, values, res.rows, undefined, executionTime);
    });

    return res.rows;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing get query', err);

    // Log error asynchronously
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });

    throw err;
  }
};

export const exists = async (query: string, values: any[]): Promise<boolean> => {
  const startTime = Date.now();
  try {
    const res = await pool.query(query, values);
    const executionTime = Date.now() - startTime;
    const result = typeof res.rowCount === 'number' && res.rowCount > 0;

    // Log asynchronously without blocking
    setImmediate(() => {
      logOperation(query, values, result, undefined, executionTime);
    });

    return result;
  } catch (err) {
    const executionTime = Date.now() - startTime;
    console.error('Error executing exists query', err);

    // Log error asynchronously
    setImmediate(() => {
      logOperation(query, values, null, err, executionTime);
    });

    throw err;
  }
};

// Simple setup file for automatic audit logging
// Import this in your main application to configure audit logging

import {
  auditConfig,
  useProductionConfig,
  useDevelopmentConfig,
  enableDebugLogging,
  logInfo,
} from './audit-config';

export type AuditSetupOptions = {
  environment?: 'production' | 'development' | 'staging' | 'test';
  enableDebug?: boolean;
  logReadOperations?: boolean;
  performanceThreshold?: number;
  batchSize?: number;
  flushInterval?: number;
};

/**
 * Initialize audit logging with optional configuration
 * Call this in your main application startup
 */
export function initializeAuditLogging(options: AuditSetupOptions = {}): void {
  logInfo('Initializing automatic audit logging system...');

  // Apply environment-based configuration
  switch (options.environment) {
    case 'production':
      useProductionConfig();
      break;
    case 'development':
      useDevelopmentConfig();
      break;
    case 'staging':
      // Staging: production-like but with some debug features
      useProductionConfig();
      if (options.enableDebug !== false) {
        enableDebugLogging();
      }
      break;
    case 'test':
      // Test: minimal configuration
      auditConfig.updateConfig({
        enabled: true,
        logReadOperations: false,
        backgroundLogging: false, // Synchronous for tests
        batchSize: 1,
        flushInterval: 100,
        enableDebugLogging: false,
      });
      break;
    default:
      // Use default configuration (already loaded)
      logInfo('Using default audit configuration');
  }

  // Apply custom options
  if (options.enableDebug !== undefined) {
    auditConfig.updateConfig({ enableDebugLogging: options.enableDebug });
  }

  if (options.logReadOperations !== undefined) {
    auditConfig.updateConfig({ logReadOperations: options.logReadOperations });
  }

  if (options.performanceThreshold !== undefined) {
    auditConfig.updateConfig({ performanceThreshold: options.performanceThreshold });
  }

  if (options.batchSize !== undefined) {
    auditConfig.updateConfig({ batchSize: options.batchSize });
  }

  if (options.flushInterval !== undefined) {
    auditConfig.updateConfig({ flushInterval: options.flushInterval });
  }

  // Log final configuration
  const config = auditConfig.getConfig();
  logInfo('Audit logging initialized with configuration:', {
    enabled: config.enabled,
    logReadOperations: config.logReadOperations,
    backgroundLogging: config.backgroundLogging,
    batchSize: config.batchSize,
    flushInterval: config.flushInterval,
    enableDebugLogging: config.enableDebugLogging,
  });
}

/**
 * Quick setup for production environment
 */
export function setupForProduction(): void {
  initializeAuditLogging({
    environment: 'production',
    enableDebug: false,
    logReadOperations: false,
    performanceThreshold: 2000,
    batchSize: 20,
    flushInterval: 2000,
  });
}

/**
 * Quick setup for development environment
 */
export function setupForDevelopment(): void {
  initializeAuditLogging({
    environment: 'development',
    enableDebug: true,
    logReadOperations: true,
    performanceThreshold: 500,
    batchSize: 5,
    flushInterval: 500,
  });
}

/**
 * Quick setup for testing environment
 */
export function setupForTesting(): void {
  initializeAuditLogging({
    environment: 'test',
    enableDebug: false,
    logReadOperations: false,
  });
}

/**
 * Disable audit logging completely
 */
export function disableAuditLogging(): void {
  auditConfig.updateConfig({ enabled: false });
  logInfo('Audit logging disabled');
}

/**
 * Enable audit logging with default settings
 */
export function enableAuditLogging(): void {
  auditConfig.updateConfig({ enabled: true });
  logInfo('Audit logging enabled');
}

/**
 * Get current audit configuration status
 */
export function getAuditStatus(): {
  enabled: boolean;
  logReadOperations: boolean;
  backgroundLogging: boolean;
  batchSize: number;
  flushInterval: number;
  enableDebugLogging: boolean;
} {
  const config = auditConfig.getConfig();
  return {
    enabled: config.enabled,
    logReadOperations: config.logReadOperations,
    backgroundLogging: config.backgroundLogging,
    batchSize: config.batchSize,
    flushInterval: config.flushInterval,
    enableDebugLogging: config.enableDebugLogging,
  };
}

// Auto-initialize with default settings if NODE_ENV is set
const nodeEnv = process.env.NODE_ENV;
if (nodeEnv) {
  switch (nodeEnv) {
    case 'production':
      setupForProduction();
      break;
    case 'development':
      setupForDevelopment();
      break;
    case 'test':
      setupForTesting();
      break;
    default:
      initializeAuditLogging();
  }
} else {
  // No NODE_ENV set, use default configuration
  logInfo('Audit logging ready with default configuration');
}

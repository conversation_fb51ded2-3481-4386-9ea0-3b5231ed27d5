import type { AuditTargetType } from '../types/audit';
import type { AuditLogEntry } from '../types/audit';

type SQLOperationType = 'INSERT' | 'SELECT' | 'UPDATE' | 'DELETE';

export type SQLOperation = {
  type: SQLOperationType;
  table: string;
  columns?: string[];
  values?: any[];
  whereClause?: string;
  returningClause?: string;
};

export type AuditInfo = {
  target_type: AuditTargetType;
  target_id?: string;
  target_name?: string;
  operation_type: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE';
};

const TABLE_TO_TARGET_TYPE_MAP: Record<string, AuditTargetType> = {
  'knowledge_base': 'knowledge_base',
  'text_summary': 'text_summary',
  'user_tokens': 'user_token',
  'jobs': 'job',
  'onedrive_synced_items': 'file',
  'sharepoint_synced_items': 'file',
  'documents': 'document',
  'files': 'file',
  'folders': 'folder',
  'agents': 'agent',
};

const OPERATION_TYPE_MAP: Record<string, 'CREATE' | 'READ' | 'UPDATE' | 'DELETE'> = {
  'INSERT': 'CREATE',
  'SELECT': 'READ',
  'UPDATE': 'UPDATE',
  'DELETE': 'DELETE',
};

export class SQLAnalyzer {
  private static cache = new Map<string, SQLOperation | null>();

  static parseSQL(query: string): SQLOperation | null {
    const cached = this.cache.get(query);
    if (cached !== undefined) {
      return cached;
    }
    console.log(`--- [AUDIT LOG] --- SQL Analyzer: Parsing query: "${query}" ---`);

    try {
      const normalizedQuery = query.trim().replace(/\s+/g, ' ').toUpperCase();
      let operation: SQLOperation | null = null;

      if (normalizedQuery.startsWith('INSERT')) {
        operation = this.parseInsert(query);
      } else if (normalizedQuery.startsWith('SELECT')) {
        operation = this.parseSelect(query);
      } else if (normalizedQuery.startsWith('UPDATE')) {
        operation = this.parseUpdate(query);
      } else if (normalizedQuery.startsWith('DELETE')) {
        operation = this.parseDelete(query);
      }

      console.log(`--- [AUDIT LOG] --- SQL Analyzer: Parse result:`, { operation });
      this.cache.set(query, operation);
      return operation;
    } catch (error) {
      console.debug('Error parsing SQL:', error);
      this.cache.set(query, null);
      return null;
    }
  }

  private static parseInsert(query: string): SQLOperation | null {
    const insertRegex = /INSERT\s+INTO\s+(\w+)\s+\(([^)]+)\)\s+VALUES/i;
    const match = query.match(insertRegex);
    if (!match) return null;

    const table = match[1].toLowerCase();
    const columns = match[2].split(',').map((c) => c.trim());
    const returningMatch = query.match(/RETURNING\s+(.+)/i);

    return {
      type: 'INSERT',
      table,
      columns,
      returningClause: returningMatch ? returningMatch[1].trim() : undefined,
    };
  }

  private static parseSelect(query: string): SQLOperation | null {
    const selectRegex = /SELECT\s+(.+?)\s+FROM\s+(\w+)/i;
    const match = query.match(selectRegex);
    if (!match) return null;

    const columns = match[1].split(',').map((c) => c.trim());
    const table = match[2].toLowerCase();
    const whereMatch = query.match(/WHERE\s+(.+?)(?:\s+ORDER BY|\s+LIMIT|$)/i);

    return {
      type: 'SELECT',
      table,
      columns,
      whereClause: whereMatch ? whereMatch[1].trim() : undefined,
    };
  }

  private static parseUpdate(query: string): SQLOperation | null {
    const updateRegex = /UPDATE\s+(\w+)\s+SET/i;
    const match = query.match(updateRegex);
    if (!match) return null;

    const table = match[1].toLowerCase();
    const whereMatch = query.match(/WHERE\s+(.+?)(?:\s+RETURNING|$)/i);
    const returningMatch = query.match(/RETURNING\s+(.+)/i);

    return {
      type: 'UPDATE',
      table,
      whereClause: whereMatch ? whereMatch[1].trim() : undefined,
      returningClause: returningMatch ? returningMatch[1].trim() : undefined,
    };
  }

  private static parseDelete(query: string): SQLOperation | null {
    const deleteRegex = /DELETE\s+FROM\s+(\w+)/i;
    const match = query.match(deleteRegex);
    if (!match) return null;

    const table = match[1].toLowerCase();
    const whereMatch = query.match(/WHERE\s+(.+?)(?:\s+RETURNING|$)/i);
    const returningMatch = query.match(/RETURNING\s+(.+)/i);

    return {
      type: 'DELETE',
      table,
      whereClause: whereMatch ? whereMatch[1].trim() : undefined,
      returningClause: returningMatch ? returningMatch[1].trim() : undefined,
    };
  }

  static extractAuditInfo(sqlOperation: SQLOperation, values: any[], result?: any): AuditInfo {
    const target_type = TABLE_TO_TARGET_TYPE_MAP[sqlOperation.table] || 'document';
    const operation_type = OPERATION_TYPE_MAP[sqlOperation.type];
    
    let target_id: string | undefined;
    let target_name: string | undefined;
    
    if (sqlOperation.type === 'INSERT' && result && result.length > 0) {
      target_id = result[0]?.id || result[0]?.knowledge_base_id || values[0];
      target_name = result[0]?.name || result[0]?.file_name || result[0]?.folder_name || 
                   this.generateTargetName(sqlOperation.table, target_id);
    } else if (sqlOperation.type === 'SELECT' || sqlOperation.type === 'UPDATE' || sqlOperation.type === 'DELETE') {
      target_id = this.extractIdFromWhere(sqlOperation.whereClause, values);
      target_name = this.generateTargetName(sqlOperation.table, target_id);
    }
    
    return {
      target_type,
      target_id: target_id ? String(target_id) : undefined,
      target_name,
      operation_type,
    };
  }

  private static extractIdFromWhere(whereClause?: string, values?: any[]): string | undefined {
    if (!whereClause || !values || values.length === 0) {
      return undefined;
    }
    
    const idPatterns = [
      /(?:^|\s)id\s*=\s*\$(\d+)/i,
      /(?:^|\s)knowledge_base_id\s*=\s*\$(\d+)/i,
      /(?:^|\s)user_id\s*=\s*\$(\d+)/i,
      /(?:^|\s)file_id\s*=\s*\$(\d+)/i,
    ];
    
    for (const pattern of idPatterns) {
      const match = whereClause.match(pattern);
      if (match) {
        const paramIndex = parseInt(match[1]) - 1;
        if (paramIndex >= 0 && paramIndex < values.length) {
          return String(values[paramIndex]);
        }
      }
    }
    
    return values[0] ? String(values[0]) : undefined;
  }

  private static generateTargetName(table: string, targetId?: string): string {
    const tableDisplayNames: Record<string, string> = {
      'knowledge_base': 'Knowledge Base',
      'text_summary': 'Text Summary',
      'user_tokens': 'User Token',
      'jobs': 'Job',
      'onedrive_synced_items': 'OneDrive File',
      'sharepoint_synced_items': 'SharePoint File',
      'documents': 'Document',
      'files': 'File',
      'folders': 'Folder',
      'agents': 'Agent',
    };
    
    const displayName = tableDisplayNames[table] || table;
    return targetId ? `${displayName} ${targetId}` : displayName;
  }

  static shouldSkipAuditLogging(sqlOperation: SQLOperation): boolean {
    const skipTables = ['audit_logs', 'pg_stat_activity', 'information_schema'];
    return skipTables.includes(sqlOperation.table.toLowerCase());
  }

  static extractDataFromValues(sqlOperation: SQLOperation, values: any[]): Record<string, any> {
    const data: Record<string, any> = {};
    
    if (sqlOperation.columns && values) {
      sqlOperation.columns.forEach((column, index) => {
        if (index < values.length) {
          data[column] = values[index];
        }
      });
    } else if (values && values.length > 0) {
      values.forEach((value, index) => {
        data[`param_${index + 1}`] = value;
      });
    }
    
    return data;
  }
}

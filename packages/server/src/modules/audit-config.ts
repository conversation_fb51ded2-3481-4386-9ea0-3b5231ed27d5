export type AuditConfig = {
  enabled: boolean;
  logReadOperations: boolean;
  logSystemOperations: boolean;
  backgroundLogging: boolean;
  batchSize: number;
  flushInterval: number;
  maxRetries: number;
  skipTables: string[];
  skipOperations: string[];
  performanceThreshold: number; // ms - log slow queries
  enableDebugLogging: boolean;
};

class AuditConfigManager {
  private static instance: AuditConfigManager;
  private config: AuditConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): AuditConfigManager {
    if (!AuditConfigManager.instance) {
      AuditConfigManager.instance = new AuditConfigManager();
    }
    return AuditConfigManager.instance;
  }

  private loadConfig(): AuditConfig {
    return {
      // Core settings - hardcoded defaults for out-of-the-box functionality
      enabled: true,
      logReadOperations: false, // Disabled by default for performance
      logSystemOperations: false, // Disabled by default to reduce noise
      backgroundLogging: true,

      // Performance settings - optimized defaults
      batchSize: 10,
      flushInterval: 1000, // 1 second
      maxRetries: 3,
      performanceThreshold: 1000, // 1 second - flag slow queries

      // Skip settings - sensible defaults to avoid logging system tables
      skipTables: ['logs', 'pg_stat_activity', 'information_schema'],
      skipOperations: [], // Don't skip any operations by default

      // Debug settings - disabled by default for production
      enableDebugLogging: false,
    };
  }

  public getConfig(): AuditConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<AuditConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  public isEnabled(): boolean {
    return this.config.enabled;
  }

  public shouldLogReadOperations(): boolean {
    return this.config.logReadOperations;
  }

  public shouldLogSystemOperations(): boolean {
    return this.config.logSystemOperations;
  }

  public shouldSkipTable(table: string): boolean {
    return this.config.skipTables.includes(table.toLowerCase());
  }

  public shouldSkipOperation(operation: string): boolean {
    return this.config.skipOperations.includes(operation.toUpperCase());
  }

  public isSlowQuery(executionTime: number): boolean {
    return executionTime > this.config.performanceThreshold;
  }

  public shouldUseBackgroundLogging(): boolean {
    return this.config.backgroundLogging;
  }

  public getBatchSize(): number {
    return this.config.batchSize;
  }

  public getFlushInterval(): number {
    return this.config.flushInterval;
  }

  public getMaxRetries(): number {
    return this.config.maxRetries;
  }

  public isDebugEnabled(): boolean {
    return this.config.enableDebugLogging;
  }
}

export const auditConfig = AuditConfigManager.getInstance();

export function logDebug(message: string, ...args: any[]): void {
  if (auditConfig.isDebugEnabled()) {
    console.debug(`[AUDIT] ${message}`, ...args);
  }
}

export function logInfo(message: string, ...args: any[]): void {
  console.log(`[AUDIT] ${message}`, ...args);
}

export function logError(message: string, ...args: any[]): void {
  console.error(`[AUDIT] ${message}`, ...args);
}

// Helper functions for easy configuration
export function enableDebugLogging(): void {
  auditConfig.updateConfig({ enableDebugLogging: true });
  logInfo('Debug logging enabled');
}

export function disableDebugLogging(): void {
  auditConfig.updateConfig({ enableDebugLogging: false });
  logInfo('Debug logging disabled');
}

export function enableReadOperationLogging(): void {
  auditConfig.updateConfig({ logReadOperations: true });
  logInfo('Read operation logging enabled');
}

export function disableReadOperationLogging(): void {
  auditConfig.updateConfig({ logReadOperations: false });
  logInfo('Read operation logging disabled');
}

export function setPerformanceThreshold(milliseconds: number): void {
  auditConfig.updateConfig({ performanceThreshold: milliseconds });
  logInfo(`Performance threshold set to ${milliseconds}ms`);
}

export function setBatchSize(size: number): void {
  auditConfig.updateConfig({ batchSize: size });
  logInfo(`Batch size set to ${size}`);
}

export function setFlushInterval(milliseconds: number): void {
  auditConfig.updateConfig({ flushInterval: milliseconds });
  logInfo(`Flush interval set to ${milliseconds}ms`);
}

// Quick configuration presets
export function useProductionConfig(): void {
  auditConfig.updateConfig({
    enabled: true,
    logReadOperations: false,
    logSystemOperations: false,
    backgroundLogging: true,
    batchSize: 20,
    flushInterval: 2000,
    enableDebugLogging: false,
    performanceThreshold: 2000,
  });
  logInfo('Production configuration applied');
}

export function useDevelopmentConfig(): void {
  auditConfig.updateConfig({
    enabled: true,
    logReadOperations: true,
    logSystemOperations: true,
    backgroundLogging: true,
    batchSize: 5,
    flushInterval: 500,
    enableDebugLogging: true,
    performanceThreshold: 500,
  });
  logInfo('Development configuration applied');
}

export function useMinimalConfig(): void {
  auditConfig.updateConfig({
    enabled: true,
    logReadOperations: false,
    logSystemOperations: false,
    backgroundLogging: true,
    batchSize: 50,
    flushInterval: 5000,
    enableDebugLogging: false,
    performanceThreshold: 5000,
  });
  logInfo('Minimal configuration applied');
}

import { Worker } from 'worker_threads';
import { pool } from './postgres';
import { auditConfig } from './audit-config';
import type { AuditLogEntry } from '../types/audit';

export type LogEntry = {
  id: string;
  entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>;
  timestamp: number;
  retries: number;
};

class BackgroundLogger {
  private static instance: BackgroundLogger;
  private logQueue: LogEntry[] = [];
  private isProcessing = false;
  private flushTimer?: NodeJS.Timeout;

  private constructor() {
    this.startFlushTimer();
    this.setupGracefulShutdown();
  }

  public static getInstance(): BackgroundLogger {
    if (!BackgroundLogger.instance) {
      BackgroundLogger.instance = new BackgroundLogger();
    }
    return BackgroundLogger.instance;
  }

  public async logAsync(entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<void> {
    const logEntry: LogEntry = {
      id: this.generateId(),
      entry,
      timestamp: Date.now(),
      retries: 0,
    };

    console.log(`--- [AUDIT LOG] --- STEP 5: logAsync received. Pushing to queue. ID: ${logEntry.id} ---`);
    this.logQueue.push(logEntry);

    if (this.logQueue.length >= auditConfig.getBatchSize()) {
      setImmediate(() => this.processQueue());
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      if (this.logQueue.length > 0) {
        this.processQueue();
      }
    }, auditConfig.getFlushInterval());
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return;
    }
    console.log(`--- [AUDIT LOG] --- STEP 6: processQueue started. Processing ${this.logQueue.length} items. ---`);

    this.isProcessing = true;

    try {
      const batch = this.logQueue.splice(0, auditConfig.getBatchSize());
      await this.processBatch(batch);
    } catch (error) {
      console.error('Error processing audit log batch:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processBatch(batch: LogEntry[]): Promise<void> {
    const successfulEntries: string[] = [];
    const failedEntries: LogEntry[] = [];
    console.log(`--- [AUDIT LOG] --- STEP 7: processBatch started for ${batch.length} logs. ---`);

    for (const logEntry of batch) {
      try {
        await this.insertAuditLog(logEntry.entry);
        console.log(`[AUDIT LOG] Log insertion SUCCESS. ID: ${logEntry.id}`);
        successfulEntries.push(logEntry.id);
      } catch (error) {
        console.error(`Failed to insert audit log ${logEntry.id}:`, error);
        console.log(`[AUDIT LOG] Log insertion FAILED. ID: ${logEntry.id}. Retries left: ${auditConfig.getMaxRetries() - logEntry.retries - 1}`);
        
        logEntry.retries++;
        if (logEntry.retries < auditConfig.getMaxRetries()) {
          failedEntries.push(logEntry);
        } else {
          console.error(`Audit log ${logEntry.id} exceeded max retries, dropping`);
        }
      }
    }

    if (failedEntries.length > 0) {
      this.logQueue.unshift(...failedEntries);
    }

    if (successfulEntries.length > 0) {
      console.debug(`Successfully logged ${successfulEntries.length} audit entries`);
    }
  }

  private async insertAuditLog(entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<void> {
    console.log(`--- [AUDIT LOG] --- STEP 8: insertAuditLog running for action: ${entry.action} ---`);
    const query = `
      INSERT INTO logs (
        action, action_type, target_type, target_id, target_name, username,
        changes, detail, success, error_message
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `;

    const detail = entry.metadata ? JSON.stringify(entry.metadata) : null;

    const values = [
      entry.action,
      entry.action.toLowerCase(),
      entry.target_type,
      entry.target_id,
      entry.target_name,
      entry.username,
      entry.changes ? JSON.stringify(entry.changes) : null,
      detail,
      entry.success ?? true,
      entry.error_message,
    ];

    await pool.query(query, values);
  }

  private generateId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public async flush(): Promise<void> {
    while (this.logQueue.length > 0) {
      await this.processQueue();
      if (this.logQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  public getQueueSize(): number {
    return this.logQueue.length;
  }

  public getStats(): { queueSize: number; isProcessing: boolean } {
    return {
      queueSize: this.logQueue.length,
      isProcessing: this.isProcessing,
    };
  }

  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('Shutting down background logger...');
      if (this.flushTimer) {
        clearInterval(this.flushTimer);
      }
      await this.flush();
      console.log('Background logger shutdown complete');
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
    process.on('beforeExit', shutdown);
  }
}

export const backgroundLogger = BackgroundLogger.getInstance();

export async function logAuditEntryAsync(entry: Omit<AuditLogEntry, 'id' | 'created_at' | 'updated_at'>): Promise<void> {
  try {
    await backgroundLogger.logAsync(entry);
  } catch (error) {
    console.error('Failed to queue audit log entry:', error);
  }
}

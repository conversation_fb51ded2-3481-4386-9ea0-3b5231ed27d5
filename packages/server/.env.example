PORT=3000

S3_STORAGE_BUCKET_NAME=
S3_STORAGE_ACCESS_KEY_ID=
S3_STORAGE_SECRET_ACCESS_KEY=
S3_STORAGE_REGION=us-east-1

REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=

DATABASE_PORT=
DATABASE_HOST=
DATABASE_NAME=
DATABASE_USER=
DATABASE_PASSWORD=

BASE_URL=
X_AUTH_SECRET_KEY=

TRIGGER_SECRET_KEY=
TRIGGER_API_URL=https://trigger.1882.studio.ai.vn
TRIGGER_PROJECT_ID=

MEILISEARCH_HOST=https://search.cmcts1.studio.ai.vn
MEILISEARCH_API_KEY=

DRIVE_REDIRECT_URI=http://localhost:5173/redirect
DRIVE_CLIENT_SECRET=
DRIVE_CLIENT_ID=

SHAREPOINT_REDIRECT_UI_URL=https://dev.cagent.cmcts.ai/document-stores-new

# Note: Automatic Audit Logging is configured via code (see audit-config.ts)
# No environment variables needed - works out-of-the-box with sensible defaults